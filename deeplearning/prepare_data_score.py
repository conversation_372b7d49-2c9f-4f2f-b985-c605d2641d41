import os
import json
import pandas as pd
from datetime import timedelta
from core_utils.base_eval import BaseEval
import re
import numpy as np
from core_utils.base_eval import TickerEval
from pathos.multiprocessing import ProcessingPool as Pool
import sys
from joblib import Memory
import xgboost as xgb
from scipy.stats import zscore
from collections import Counter
from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST

from core_utils.redis_cache import EvalRedis

memory = Memory(location=f'{JOBLIB_CACHE_DIR}_tuning', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host=REDIS_HOST, db=1)

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/deeplearning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

# 1. Load filter.json
FILTER_PATH = 'report/filter.json'
TICKER_PATH = 'ticker_v1a/'
SAVE_PATH = 'deeplearning/dl_train.csv'


class GetData(BaseEval):
    def __init__(self, stock, df_all, dict_filter, cache_service):
        super().__init__(stock, df_all, dict_filter, cache_service=cache_service)

    @TickerEval.cache_prefix_result(unique=False, expiry=1800)
    def sell_signals(self):
        # Apply sell filters
        now = self.df_all.iloc[-1:].copy()
        now['filter'] = 'Hold'

        sell_data = [now]

        for f in self.dictFilter:
            if f.startswith('~'):
                try:
                    pd_Sell_filtered = self.pd_query(f, self.dictFilter[f])
                    sell_data.append(pd_Sell_filtered)
                except Exception as e:
                    # print(f"{self.ticker}--{f[1:]}--error: {e}")
                    pass

        _sell_data = [data for data in sell_data if not data.empty]
        pd_sell = pd.concat(_sell_data, axis=0).sort_values('time', ascending=True)
        pd_sell['month'] = pd_sell['time'].map(lambda x: x[:7])
        pd_sell['week'] = pd.to_datetime(pd_sell['time']).dt.strftime('%Y-%W')

        return pd_sell

    @TickerEval.cache_prefix_result(unique=False, expiry=1800)
    def buy_signals(self):
        # Apply buy filters
        buy_data = []

        for f in self.dictFilter:
            if f.startswith('_'):
                try:
                    pd_buy_filtered = self.pd_query(f, self.dictFilter[f])
                    buy_data.append(pd_buy_filtered)
                except Exception as e:
                    # print(f"{self.ticker}--{f[1:]}--error: {e}")
                    pass

        _buy_data = [data for data in buy_data if not data.empty]
        pd_buy = pd.concat(_buy_data, axis=0).sort_values('time', ascending=True) if _buy_data else buy_data[-1]

        pd_buy['month'] = pd_buy['time'].map(lambda x: x[:7])
        pd_buy['week'] = pd.to_datetime(pd_buy['time']).dt.strftime('%Y-%W')

        return pd_buy

    def pd_query(self, f, v):
        f_col = "filter"
        result = self.df_all.query(v).copy()
        result[f_col] = f[1:]

        return result


def parse_indicators_from_filter(dict_filter):
    indicators = []
    dict_filter = {k: v for k, v in dict_filter.items() if
                   (k.startswith('_') or k.startswith('~'))}  # Remove empty filters

    for key, value in dict_filter.items():
        indicator = re.findall(r'\b[A-Za-z_]\w*\b', value)
        indicator = [var for var in indicator if not var.isdigit() and var.lower() not in
                     ('and', 'or', 'not', 'if', 'else', 'true', 'false', 'null', 'none', 'in', 'is', 'ge', 'le',
                      'gt', 'lt', 'eq', 'ne', '>=', '<=', '>', '<', '==', '!=', '(', ')', '[', ']', '{', '}',
                      '&', '|', 'abs', 'acos', 'acosh', 'asin', 'asinh', 'atan', 'atanh', 'ceil', 'cos',
                      'cosh')]
        indicators.extend(indicator)

    init_cols = ['time', 'ticker', 'EVEB_MA5Y', 'EVEB_MA1Y', 'EVEB_MA3M', 'EVEB_SD5Y', 'EVEB_SD1Y', 'EVEB_SD3M', 'EVEB',
                 'ROIC_Trailing', 'ROIC3Y', 'ROIC5Y', 'ROIC_Min3Y', 'ROIC_Min5Y', 'ROA_P0',
                 'EBITM_P0', 'NPM_P0', 'EBITDA_P0', 'CashR_P0', 'QuickR_P0', 'FinLev_P0', 'AssetTurn_P0', 'FATurn_P0',
                 'DSO_P0', 'DIO_P0', 'DPO_P0', 'CashCycle_P0', 'InvTurn_P0', 'STLTDebt_Eq_P0', 'Debt_Eq_P0', 'FA_Eq_P0',
                 'OwnEq_Cap_P0']

    indicators = init_cols + indicators
    indicators = list(set(indi.replace(' ', '') for indi in indicators))

    return indicators


def parse_formula_from_filter(dict_filter):
    def extract_simple_thresholds(dict_filter):
        dict_filter = {k: v for k, v in dict_filter.items() if
                       (k.startswith('_') or k.startswith('~'))}
        simple_formula = []
        pattern = r'\(\s*([a-zA-Z_]\w*)\s*(==|!=|>=|<=|>|<)\s*([-+]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)\s*\)'

        for key, value in dict_filter.items():
            value = re.sub(r'\b[a-zA-Z_]\w*\s*\([^()]*\)', '', value)

            matches = re.findall(pattern, value)
            for A, op, num in matches:
                simple_formula.append(A)

        final_indicators = []
        remove_indicators = {'Price', 'Inflation_7', 'Close', 'Open', 'OShares', 'BVPS'}
        prefixes = ('MA', 'Inflation', 'VAP', 'Sup', 'Res', 'NP', 'ID')
        exclude_substrings = ('Close', 'Volume', 'ID', 'Invest', 'CF_OA', 'HI', 'LO')
        for indi in simple_formula:
            if indi in remove_indicators:
                continue
            if indi.startswith(prefixes):
                continue
            if any(sub in indi for sub in exclude_substrings):
                continue
            final_indicators.append(indi)

        indicators = set(indi.replace(' ', '') for indi in final_indicators)

        return indicators

    def transfrom_formular(dict_filter):
        dict_filter = {k: v for k, v in dict_filter.items() if
                       (k.startswith('_') or k.startswith('~'))}  # Remove empty filters

        ratio_list = []
        pattern = r'([a-zA-Z_][\w]*)\s*([<>]=?|==|!=)\s*([\d.]+)\s*\*\s*([a-zA-Z_][\w]*)'
        for key, value in dict_filter.items():
            matches = re.findall(pattern, value)

            for A, op, x, B in matches:
                ratio = f"{A}/{B}"
                ratio_list.append(ratio)

        return set(ratio_list)

    def all_indicators():

        init_cols = ['time', 'ticker', 'EVEB_MA5Y', 'EVEB_MA1Y', 'EVEB_MA3M', 'EVEB_SD5Y', 'EVEB_SD1Y', 'EVEB_SD3M',
                     'EVEB', 'ROIC_Trailing', 'ROIC3Y', 'ROIC5Y', 'ROIC_Min3Y', 'ROIC_Min5Y', 'ROA_P0/ROA_P4',
                     'EBITM_P0/EBITM_P4', 'NPM_P0/NPM_P4', 'CashR_P0/CashR_P4', 'QuickR_P0/QuickR_P4',
                     'FinLev_P0/FinLev_P4', 'AssetTurn_P0/AssetTurn_P4', 'CR_P0/CR_P4', 'FAssetTurn_P0/FAssetTurn_P4',
                     'DSO_P0/DSO_P4', 'DIO_P0/DIO_P4', 'DPO_P0/DPO_P4', 'CashCycle_P0/CashCycle_P4',
                     'InvTurn_P0/InvTurn_P4', 'STLTDebt_Eq_P0/STLTDebt_Eq_P4', 'Debt_Eq_P0/Debt_Eq_P4',
                     'FAsset_Eq_P0/FAsset_Eq_P4', 'OwnEq_Cap_P0/OwnEq_Cap_P4', 'Revenue_YoY_P0',
                     'PB_MA5Y', 'PE_MA5Y', 'PE_SD5Y', 'PB_SD5Y', 'PB_SD1Y', 'PB_MA1Y', 'PE_SD1Y', 'PE_MA1Y',
                     'Volume*Price / Trading_Session', 'Risk_Rating', 'VNINDEX_PE']

        indicators = set(indi.replace(' ', '') for indi in init_cols)
        return indicators

    def all_formulas(dict_filter):
        dict_filter = {k: v for k, v in dict_filter.items() if
                       (k.startswith('_') or k.startswith('~'))}  # Remove empty filters

        all_filter_expr = json.dumps(dict_filter)

        math_expr_pattern = re.compile(r'''
            (                                   # Bắt đầu nhóm lớn
                (?:                             # Nhóm không bắt (?:) cho biến hoặc biểu thức con
                    [A-Za-z_][A-Za-z0-9_]*      # Biến/tên chỉ báo (bắt đầu bằng chữ hoặc _)
                    (?:\([^\)]*\))?             # Có thể có dạng hàm () bên cạnh (optional)
                    \s*                         # Có thể có space
                    [\/*+\-]                    # Phép toán bắt buộc
                    \s*                         # Có thể có space
                ){1,}                           # Lặp ít nhất 1 lần (tức là phải có ít nhất 2 biến)
                [A-Za-z_][A-Za-z0-9_]*          # Biến kết thúc
                (?:\([^\)]*\))?                 # Có thể có dạng hàm () bên cạnh
            )
        ''', re.VERBOSE)

        indicator_formulas = set(math_expr_pattern.findall(all_filter_expr))

        indicator_formulas = set(
            re.sub(r'\)+$', '', expr.replace(' ', ''))  # Remove ngoặc ở cuối và space
            for expr in indicator_formulas
            if not re.match(r'^[\d.eE+\-*/]+$', expr)  # Không lấy cụm toàn số/toán tử
        )

        return indicator_formulas

    def all_bracket_expr(dict_filter):
        dict_filter = {k: v for k, v in dict_filter.items() if
                       (k.startswith('_') or k.startswith('~'))}  # Remove empty filters

        all_filter_expr = json.dumps(dict_filter)

        pattern = re.compile(
            r'''(
                (?:[A-Za-z_][A-Za-z0-9_]*\([^)]*\)|    # Hàm dạng func(...)
                \([A-Za-z0-9_+\-*/. ]+\)|              # Hoặc biểu thức trong ngoặc (...)
                [A-Za-z_][A-Za-z0-9_]*                 # Hoặc chỉ là biến
                )
                \s*[/\*+\-]\s*
                (?:[A-Za-z_][A-Za-z0-9_]*\([^)]*\)|    # Hàm
                \([A-Za-z0-9_+\-*/. ]+\)|              # Ngoặc
                [A-Za-z_][A-Za-z0-9_]*
                )
            )''', re.VERBOSE
        )

        bracket_exprs = set(pattern.findall(all_filter_expr))
        bracket_exprs = set(expr.replace(' ', '') for expr in bracket_exprs)

        return bracket_exprs

    cols_00 = all_indicators()
    cols_01 = transfrom_formular(dict_filter)
    cols_02 = all_formulas(dict_filter)
    cols_03 = all_bracket_expr(dict_filter)
    cols_04 = extract_simple_thresholds(dict_filter)

    full_expr = cols_00.union(cols_01).union(cols_02).union(cols_03).union(cols_04)

    final_expr = []
    for col in full_expr:
        ops = re.findall(r'[\+\-\*/]', col)
        counter = Counter(ops)
        total_count = sum(counter.values())
        if total_count > 0:
            if ((counter.get('+', 0) + counter.get('*', 0) + counter.get('-', 0)) == total_count) and not (
                    (counter.get('-', 0) == total_count) and 'ID' in col):
                continue

        final_expr.append(col)

    return sorted(final_expr)


# Add new indicator columns to df_buy
def enrich_with_indicators(df, full_expr=None):
    df = df.copy()

    def safe_eval_formula(row, formula):
        try:
            python_funcs = {'abs', 'min', 'max', 'round', 'sum', 'np', 'float', 'int'}
            # Replace variable names with row['var']
            vars_in_formula = set(re.findall(r'[A-Za-z_][A-Za-z0-9_]*', formula))
            expr = formula
            for var in vars_in_formula:
                if var not in python_funcs and var in row:
                    expr = re.sub(rf'\b{var}\b', f'row["{var}"]', expr)
            return eval(expr, {'np': np, 'abs': abs, 'min': min, 'max': max, 'sum': sum, 'round': round, 'float': float,
                               'int': int}, {'row': row})
        except ZeroDivisionError:
            return 0

        except Exception:
            return np.nan

    all_full_expr = []
    for expr in full_expr:
        if expr not in df.columns:
            all_full_expr.append(expr)

    df[all_full_expr] = np.nan
    for formula in full_expr:
        colname = formula
        df[colname] = df.apply(lambda row: safe_eval_formula(row, formula), axis=1)
    return df


# 4. For each buy, calculate profit at 2w, 1m, 3m (10, 20, 60 trading days)
def calc_profits(df, periods=[10, 20, 60]):
    for p, label in zip(periods, ['2W', '1M', '3M']):
        df[f'profit_{label}'] = (df['Close'].shift(-p) / df['Close'] - 1) * 100
    return df


def calc_profits_v1(df, periods=[10, 20, 60]):
    profit_cols = []

    center_dict_v1 = {
        '2W': 5,
        '1M': 10,
        '3M': 20
    }
    center_dict_v2 = {
        '2W': 3,
        '1M': 7,
        '3M': 15
    }
    for p, label in zip(periods, ['2W', '1M', '3M']):
        df[f'profit_{label}'] = (df['Close'].shift(-p) / df['Close'] - 1) * 100
        profit_cols.append(f'profit_{label}')

    for p, label in zip(periods, ['2W', '1M', '3M']):
        # Tính trung bình 5 ngày, centered tại t + p
        future_avg = df['Close'].shift(-p).rolling(window=center_dict_v1[label], center=True).mean()
        df[f'profit_{label}_center_{center_dict_v1[label]}'] = (future_avg / df['Close'] - 1) * 100
        profit_cols.append(f'profit_{label}_center_{center_dict_v1[label]}')

    for p, label in zip(periods, ['2W', '1M', '3M']):
        # Tính trung bình 5 ngày, centered tại t + p
        future_avg = df['Close'].shift(-p).rolling(window=center_dict_v2[label], center=True).mean()
        df[f'profit_{label}_center_{center_dict_v2[label]}'] = (future_avg / df['Close'] - 1) * 100
        profit_cols.append(f'profit_{label}_center_{center_dict_v2[label]}')

    return df, profit_cols


def fill_data(df):
    fundamental_cols = [
        'PB_SD1Y', 'PB_MA1Y', 'PE_SD1Y', 'PE_MA1Y', 'PB_SD5Y', 'PB_MA5Y', 'PE_SD5Y', 'PE_MA5Y', 'PB', 'BVPS', 'PE',
        'PCF', 'OShares', 'LtDebt_P0', 'CF_OA_P0', 'CF_OA_P1', 'CF_OA_P2', 'CF_OA_P3', 'CF_Invest_P0', 'CF_Invest_P1',
        'CF_Invest_P2', 'CF_Invest_P3', 'NP_P0', 'NP_P1', 'NP_P2', 'NP_P3', 'NP_P4'
    ]

    FFILL_LIMIT_QUARTER = 66
    for col in fundamental_cols:
        # flag_col = f'has_{col}'
        # df[flag_col] = (~df[col].isnull()).astype(int)
        df.loc[:, col] = df[col].ffill(limit=FFILL_LIMIT_QUARTER)

    fill_0_cols = ['Volume']
    df.loc[:, fill_0_cols] = df[fill_0_cols].fillna(0)


def preprocess_ticker_data(df):
    """
    Preprocess ticker data by reading from CSV, filling missing values, and returning a DataFrame.
    """
    # df = df[df['Volume_1M_P50'] > 5e3]

    df['zero_volume'] = (df['Volume'] < 1000).astype(int)
    df['zero_streak'] = df['zero_volume'].rolling(5).sum()
    df = df[df['zero_streak'] < 5]
    df = df.drop(['zero_volume', 'zero_streak'], axis=1)

    # 11. Remove abnormal price gaps (open khác close hôm trước >10%)
    # price_gap = np.abs(df['Open'] / df['Close'].shift(1) - 1)
    # df = df[price_gap < 0.1]

    # df = df[(np.abs(zscore(df['close'])) < 3)]

    return df


def preprocess_datasets(df_all):
    df_all = df_all.set_index(['symbol', 'date']).sort_index()

    # 3. Remove symbols missing >30% phiên
    missing = df_all.isnull().groupby('symbol').mean().mean(axis=1)
    keep_symbols = missing[missing < 0.3].index
    df_all = df_all.loc[keep_symbols]

    num_cols = df_all.select_dtypes(include=np.number).columns
    for col in num_cols:
        low = df_all[col].quantile(0.01)
        high = df_all[col].quantile(0.99)
        df_all[col] = df_all[col].clip(lower=low, upper=high)


@memory.cache
def get_signals_with_pool(dictFilter):
    def eval(ticker):
        try:
            df = pd.read_csv(f'{TICKER_PATH}{ticker}.csv')
            df = df.query("(time >= '2014-01-01') & (time <= '2026-01-01')")

            if (df['Close'].mean() < 5000 and df['Volume'].mean() < 20000) or (df['Volume'] < 10000).mean() > 0.8:
                pd_temp = df[['time', 'ticker', 'Volume', 'Volume_1M_P50', 'Close']].copy()
                print(f"No signals for {ticker}")
                return None, pd_temp

            fill_data(df)
            # You may need to adjust this import/class to your project
            eval_ticker = GetData(ticker, df, dictFilter, cache_service=redis_cache)
            df_buy = eval_ticker.df_buy
            # df_sell = eval_ticker.df_sell
            if df_buy.shape[0] == 0 or df_buy['filter'].unique().shape[0] == 0:
                if not ((df['Volume_1M_P50'].mean() > 30e3 and df['Volume'].mean() > 40e3 and df['Volume_1M'].tail(
                        240).mean() > 20e3) or (
                                df['Volume_1M_P50'].mean() > 20e3 and df['Volume'].mean() > 30e3 and df_buy.shape[
                            0] < 2000 and df['Volume_1M'].tail(240).mean() > 20e3)):
                    pd_temp = df[['time', 'ticker', 'Volume', 'Volume_1M_P50', 'Close']].copy()
                    print(f"No signals for {ticker}")
                    return None, pd_temp

            df_signals = df.copy()
            df_signals = df_signals.drop_duplicates(subset=['time'], keep='last').sort_values(by='time', ascending=True)

            # Enrich with additional columns
            # full_indicators = parse_indicators_from_filter(dict_filter)
            full_cols = parse_formula_from_filter(dict_filter)

            df_signals = enrich_with_indicators(df_signals, full_cols)

            # full_cols = full_indicators + full_exprs
            full_cols = [col for col in full_cols if col in df_signals.columns]

            # Add label for df_signals
            df_signals, profit_cols = calc_profits_v1(df_signals, periods=[10, 20, 60])
            full_cols = full_cols + profit_cols
            # drop if label is None ỏ np.nan
            df_signals = df_signals.dropna(subset=profit_cols, how='any')

            df_signals = preprocess_ticker_data(df_signals)

            df_signals = df_signals[full_cols]
            return df_signals.reset_index(drop=True), None

        except Exception as e:
            print(f"Error: {ticker}: {e}")
            return None, None

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(TICKER_PATH) if f.endswith('.csv')]
    num_procs = 20
    print("Numbers of ticker", len(list_processed_ticker))
    with Pool(num_procs) as p:
        # lres = p.amap(eval, list_processed_ticker)
        lres = p.map(eval, list_processed_ticker)
        # lres.extend(lres.get())

    # lres = [res for res in lres if res is not None and not res.empty]

    pd_00 = [res[0] for res in lres if res[0] is not None and not res[0].empty]
    pd_01 = [res[1] for res in lres if res[1] is not None and not res[1].empty]

    pd_deal = pd.concat(pd_00, axis=0)
    pd_ignore = pd.concat(pd_01, axis=0)
    pd_ignore.to_csv('deeplearning/pd_ignore.csv', index=False)
    mean_null = pd_deal.isnull().mean()
    # drop_cols = []
    # for col in pd_deal.columns:
    #     if mean_null[col] > 0.15 and col not in ['profit_2W', 'profit_1M', 'profit_3M']:
    #         # print(f"Column {col} has {mean_null[col] * 100:.2f}% missing values, dropping it.")
    #         drop_cols.append(col)
    #     # else:
    #     #     print(f"Column {col} has {mean_null[col] * 100:.2f}% missing values, keeping it.")
    #
    # pd_deal.drop(columns=drop_cols, inplace=True)
    print("Numbers of keep ticker: ", len(pd_deal['ticker'].unique()))
    return pd_deal.reset_index(drop=True)


def split_tvt(pdxy, cname_tvt='tvt', test_size=10, YMD_test='2022-01-01'):
    """
    Split data into train, val, test
    """
    list_ticker = list(pdxy['ticker'].unique())
    list_ticker_test = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]

    pdxy[cname_tvt] = 'other'
    tTest = pdxy['ticker'].isin(list_ticker_test)
    tTrain = ~tTest

    # Sort by time
    pdxy = pdxy.sort_values(by='time')

    iA = pdxy['time'] < YMD_test
    iB = pdxy['time'] > (pd.to_datetime(YMD_test) + timedelta(days=100)).strftime('%Y-%m-%d')

    pdxy.loc[iA & tTrain, cname_tvt] = 'train'
    pdxy.loc[iB & tTrain, cname_tvt] = 'val'
    pdxy.loc[tTest, cname_tvt] = 'test'
    return pdxy


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


if __name__ == '__main__':
    dict_filter = {}
    # if os.path.exists(FILTER_PATH):
    #     with open(FILTER_PATH, 'r') as f:
    #         dict_filter = json.load(f)
    dict_filter = {
        "$BKMA200": "BearDvg2, MA21, SellLowGrowth, SellPE, SellResistance, SellResistance1M",
        "$RSILow30": "BearDvg2, MA31, S13, SellBV, SellLowGrowth, SellResistance1M, SellResistance1Y, SellVolMax",
        "$TL3M": "BearDvg2, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M",
        "$BuySupport": "BearDvg2, SellBV, SellLowGrowth, SellResistance, SellResistance1Y",
        "$UnderBV": "BearDvg2, MA21, MA41, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellResistance1Y, SellVolMax",
        "$TrendingGrowth": "BearDvg2, MA41, SellBV, SellBV2, SellLowGrowth, SellPE, SellResistance, SellVolMax",
        "$SuperGrowth": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellVolMax",
        "$SurpriseEarning": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
        "$Conservative": "MA21, MA41, SellLowGrowth, SellPE, SellResistance, SellResistance1M, SellResistance1Y",
        "$BullDvg": "BearDvg2, MA41, S13, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1Y",
        "$VolMax1Y": "BearDvg2, MA21, MA31, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
        "Init_s": "(Volume_1M_P50*Price>3e+8*Inflation_7) & (time>='2014-01-01') & (time<='2026-01-01')",
        "Init": "(Volume_1M_P50*Price>1e+9*Inflation_7) & (time>='2014-01-01') & (time<='2026-01-01')",
        "_BKMA200": "{Init} &((ID_LO_3Y-ID_HI_3Y)>250.0) & (MA50/MA200>0.87) & (MA10/MA200<1.48) & (ROE5Y >0.115) & (PE <11.6) & (NP_P0 > 1.1*NP_P1) & (NP_P1 > 0) & (HI_3M_T1/LO_3M_T1<2.1)",
        "_TrendingGrowth": "{Init} &(Close> 1.0*Volume_Max5Y_High) & (ROE_Min5Y > 0.04)&(PE<=10.2)& (NP_P0 > 1.15*NP_P1) & (NP_P1 > NP_P2) & (PE >2.4)& (HI_3M_T1/LO_3M_T1<2.2)",
        "_TL3M": "{Init} &(HI_3M_T1/LO_3M_T1<1.36) & (Volume > 1.23*Volume_3M_P90)& (ROE5Y>0.07) & (PE<10.0) & (PB < 1.9) & (FSCORE > 1.0) & (NP_P0 > 1.2*NP_P1) & (PCF>0.4) & (NP_P1 > 0) & (PE >3.0)",
        "_BuySupport": "{Init} &(Close >1.1300000000000001* Sup_1Y) & (LO_3M_T1 < 1.42*Sup_1Y) &( Close < 1.25*LO_3M_T1)  & (PE < 8.0) & (PB <4.6000000000000005) & (PCF <30.200000000000003) & (PCF >0.6000000000000001)  &  ((Cash_P0/ (LtDebt_P0+1) > 0.015)|abs(IntCov_P0 > 7.0)) & ((CF_OA_5Y/OShares)> 8000.0) & (ROE_Min5Y > 0.105) & (ICB_Code != 2353)",
        "_RSILow30": "{Init} &(D_RSI < 0.3)  & (PE < 7.4)  & (PE>3.8) & (ROE_Min3Y > 0.05) & (PB < 0.85*PB_MA5Y - 0.55*PB_SD5Y) & (PCF > 2.4) & (PCF <27.0) & ((Cash_P0/ (LtDebt_P0+1) > 0.06)|(abs(IntCov_P0) > 3.4)) & (NP_P0 > 0)",
        "_UnderBV": "{Init} &(PB < 1.48) & (FSCORE >= 1.0) & (NP_P0 > 1.32*NP_P1)  & (PCF>1.0)  & (PE >0.0)  & (PCF < 23.0)  & ((NP_P0+NP_P1+NP_P2+NP_P3)/OShares > 1750.0) & (NP_P0/NP_P4 > 1.15)",
        "_SuperGrowth": "{Init} &(PE/((NP_P0/NP_P4 -1)*100) < 0.93) & (ROE_Min5Y > 0.035) &  ((FSCORE>=6.0)) & (NP_P0/NP_P4 > 1.31)  & (NP_P4 >= 0)  & (PCF > 0.9) & (PCF < 14.0) & (CF_OA_5Y/OShares > 8000.0) & (ID_Current -  ID_Release <= 10)",
        "_SurpriseEarning": "{Init} &(PE < 11.5) & (PB < 1.9000000000000001) & (ROE_Min5Y > 0.01) & ((NP_P0 - NP_P4)/NP_P4 > 0.18) & (NP_P0/NP_P1> 1.4000000000000001) & (NP_P1 > 0) & (PCF > 1.0) & (PCF < 16.0) & (CF_OA_5Y/OShares > 9500.0) & ((Cash_P0/ (LtDebt_P0+1) >0.04)|(abs(IntCov_P0) > 1.0))",
        "_Conservative": "(Volume*Price>3e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (((CF_OA_5Y + CF_Invest_5Y )/5)/(OShares*Price + LtDebt_P0) > 0.045) & ((CF_OA_P0+CF_OA_P1+CF_OA_P2+CF_OA_P3 + CF_Invest_P0 + CF_Invest_P1+ CF_Invest_P2+CF_Invest_P3)/(OShares*Price + LtDebt_P0)>0.11) & ((Cash_P0/ (LtDebt_P0+1) > 0.098)|(abs(IntCov_P0) > 6.0))  & (NP_P0 /NP_P1> 1.1) & (NP_P1>0) & (PE >1.2000000000000002) & (ROE_Min3Y > 0.09) & (PE < 21.0) & (NP_P0/NP_P4 > 1.05)",
        "_BullDvg": "{Init} &(D_RSI / D_RSI_T1 > 0.8) & (D_RSI > 0.46) & (D_RSI < 0.86) & (D_RSI_Min3M < 0.44) & (D_RSI_Min1W > 0.08) & (D_RSI_Min1W/D_RSI_Min3M > 1.08) &  (D_RSI_Min1W_Close/D_RSI_Min3M_Close < 1.8)  & (FSCORE > 4.0) & (PE< 11.8) &  (PE>3.4) & (PB < 3.1)  & (ROE_Min5Y > 0.025) & (PCF <22.5) & (PCF>1.0) &  ((Cash_P0/ (LtDebt_P0+1) > 0.064)|(abs(IntCov_P0) > 7.0)) & ((CF_OA_5Y/OShares)> 7800.0) & (NP_P0/NP_P4 >=1.3)",
        "_VolMax1Y": "{Init} &(Close > 1.01*Volume_Max1Y_High) & (Close_T1W < 1.08*Volume_Max1Y_High) & (Volume > 0.85*Volume_3M_P50) & (PE >1.0) & (PE < 10.8) & (PB<4.0) & (PCF > 1.0) & (((NP_P0 > 1.15*NP_P1)& (PCF < 15.8) & (ROE_Min3Y > 0.025)) | ((((NP_P0 - NP_P4)/abs(NP_P4) > 1.1)) & (PCF < 11.4)))  & (ID_Current-Volume_Max1Y_ID<=150.0)  & (Volume_Max1Y_High/LO_3M_T1 < 1.45) & (FSCORE > 3.0)",
        "_DividendYield": "{Init}   & (PCF > 0) & (PCF < 30)  & (NP_P0> 0) & (NP_P1>0) & (NP_P2>0) & (NP_P3 >0)  & (PE>0)  & (PE < 18) &  ((CF_OA_5Y/OShares)> 5000) & (abs(Dividend_Min3Y)/Price  >0.05) & (abs(Dividend_Min3Y) > 500)",
        "~MA21": "{Init_s} & ((MA20/MA50<1.04) & (MA20_T1/MA50_T1>0.96)  &  (D_RSI/D_RSI_T1W < 1.21) & (Close < 0.81*VAP1M)  & (D_MACDdiff< 7.0) & (Close/Close_T1W < 0.95)",
        "~MA31": "{Init_s} &(MA10/MA200<1) & (MA10_T1/MA200_T1>1) & (Close < 0.98*VAP3M)  & (Close/Close_T1W < 0.95)&   (D_RSI/D_RSI_T1W < 0.95) & (D_RSI < 0.5) & (D_MACDdiff< 0)",
        "~MA41": "{Init_s} &(Close > 1.5*MA200) & (NP_P0/NP_P1 < 0.88) & (Volume>0.9*Volume_3M_P50)  & (Close/Close_T1W < 0.94) & (Close < 1.01*VAP1M)",
        "~S13": "{Init_s} &(C_L1W>=1.29) & (D_CMB_Peak_T1>1.09*D_CMB) & (Close>1.12*MA10) & (D_CMB_XFast<7.0)",
        "~SellLowGrowth": "{Init_s} &(NP_P0/NP_P4 < 1.2) & (ID_Current -  ID_Release <= 10)",
        "~SellBV": "{Init_s} &(Close > 1.85*BVPS) & (NP_P0 /NP_P1 < 0.91) &(Close < 0.97*VAP1M) & (Close_T1W > 0.92*VAP1M) & (Volume > 0.95* Volume_3M_P50) & (ICB_Code != 8633)",
        "~SellBV2": "{Init_s} &(PB > 1.23*PB_MA5Y + 0.84*PB_SD5Y) & (NP_P0 /NP_P1 < 0.62)  & (Close < 0.99*VAP1M) & (Close_T1W > 0.8*VAP1M)  & (D_RSI > 0.28) & (Volume > 1.01*Volume_3M_P50)",
        "~SellPE": "{Init_s} &(PE >= 1.29*PE_MA5Y  + 0.91*PE_SD5Y) & (NP_P0 /NP_P1 < 0.95)  & (Close < 1.02*VAP3M) & (Close_T1W > 0.9*VAP3M)  & (Close/Close_T1W < 0.97)  & (Volume > 1.01*Volume_3M_P50)",
        "~SellResistance": "{Init_s} &(Open/Close< 0.95)  & (Close  <  0.89*Res_1Y)  & (Close/LO_3M_T1 > 1.22) & (Volume > 2.08*Volume_3M_P50)",
        "~SellResistance1M": "{Init_s} &(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= 15.0) & (Close < 0.97*VAP1M) & (Close_T1 >  1.0*VAP1M) & (Volume > 1.01* Volume_3M_P50)& (D_RSI > 0.33)",
        "~SellResistance1Y": "{Init_s} &(PB > 1.23*PB_MA5Y + 0.93*PB_SD5Y) & (NP_P0 /NP_P1 < 0.8)  &  (Close < 0.94*Res_1Y)  & (Volume  > 1.25*Volume_3M_P50)  & (Close_T1W > 0.86*VAP1M)  & (D_RSI > 0.43)",
        "~BearDvg2": "{Init_s} &(D_RSI_Max1W/D_RSI > 0.82)  & (D_RSI_Max3M/D_RSI_Max1W > 1)  & (D_RSI_Max3M > 0.54)& (D_RSI_Max1W < 0.7)& (D_RSI_Max1W >0.59) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.16) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.28)  & (D_RSI_T1/D_RSI > 0.99) & (Volume > 1.13*Volume_1M)",
        "~SellVolMax": "{Init_s} &(Close/Volume_MaxTop5_2Y_Close < 0.8) & (ID_Current - Volume_MaxTop5_2Y_ID <=150.0) & (Close < 1.13*VAP1W) & (D_RSI > 0.49)  & (Close/Close_T1 < 1.08) & (D_RSI/D_RSI_T1W < 1.1) & (Close_T1/LO_3M_T1 > 1.35)",
        "~BearDvgVNI": "(time>='2014-01-01') & (time<='2026-01-01') & (VNINDEX_RSI_Max1W/VNINDEX_RSI > 1.044)  & (VNINDEX_RSI_Max3M > 0.74) & (VNINDEX_RSI_Max1W < 0.72) & (VNINDEX_RSI_Max1W>0.61) & (VNINDEX_RSI_Max1W_Close/VNINDEX_RSI_Max3M_Close > 1.028) & (VNINDEX_RSI_Max3M_MACD/VNINDEX_RSI_Max1W_MACD>1.11) & (VNINDEX_MACDdiff < 0)  & ( Close/VNINDEX_RSI_Max3M_Close > 0.96) & (VNINDEX_RSI_MinT3 > 0.43) & (VNINDEX_CMF < 0.13)",

    }
    for key, value in dict_filter.items():
        dict_filter[key] = value.replace("{Init}", dict_filter['Init']).replace("{Init_s}", dict_filter['Init_s'])
    print(dict_filter)
    # 3. Get buy signals for each ticker using multiprocessing
    print("Start getting signals with multiprocessing...")
    a = parse_formula_from_filter(dict_filter)
    df_deal = get_signals_with_pool(dict_filter)

    df_deal = split_tvt(df_deal, test_size=10, YMD_test='2022-01-01')
    df_deal.to_csv(SAVE_PATH, index=False)
    # list_eval = get_list_eval(df_deal)
    pass
