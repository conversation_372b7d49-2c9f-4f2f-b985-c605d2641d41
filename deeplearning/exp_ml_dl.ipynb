#%%
import os
import sys

from tuning.sell.exp_sell import threshold

current_dir = '/home/<USER>/dev/ta/kaffa_v2'
os.chdir(current_dir)
sys.path.insert(0, current_dir)
#%%
# Load the necessary libraries
import random

import numpy as np

random.seed(123)
import os
import sys
from joblib import Memory

from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST
import seaborn as sns

from core_utils.redis_cache import EvalRedis
from datetime import timedelta
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import matplotlib.pyplot as plt
%matplotlib inline
import xgboost as xgb
from sklearn.preprocessing import StandardScaler
import numpy as np
import xgboost as xgb
from sklearn.metrics import f1_score, roc_auc_score, classification_report, accuracy_score
from sklearn.model_selection import train_test_split, RandomizedSearchCV
from sklearn.preprocessing import label_binarize
from sklearn.utils.class_weight import compute_sample_weight
from sklearn.model_selection import train_test_split
from sklearn.decomposition import PCA
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.svm import SVC
from sklearn.gaussian_process import GaussianProcessClassifier
from sklearn.gaussian_process.kernels import RBF
from sklearn.neighbors import KNeighborsClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, StackingClassifier
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
from IPython.core.display_functions import display
# from ydata_profiling import ProfileReport
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
import matplotlib.pyplot as plt
# import tensorflow as tf
# print(f'TensorFlow version: {tf.__version__}')
from sklearn.preprocessing import label_binarize

memory = Memory(location=f'{JOBLIB_CACHE_DIR}_tuning', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host=REDIS_HOST, db=1)


#%%
def show_confusion_matrix(y_true, y_pred_prob, class_names=None, normalize=None, figsize=(6, 6)):
    import numpy as np
    import matplotlib.pyplot as plt
    from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
    if len(y_pred_prob.shape) > 1:
        y_pred = np.argmax(y_pred_prob, axis=1)
    else:
        y_pred = y_pred_prob
    cm = confusion_matrix(y_true, y_pred, normalize=normalize)
    print("Confusion Matrix:")
    print(cm)
    plt.figure(figsize=figsize)
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=class_names)
    disp.plot(cmap='Blues', values_format='.2f' if normalize else 'd', ax=plt.gca(), colorbar=False)
    plt.title('Confusion Matrix' + (f' (normalized: {normalize})' if normalize else ''))
    plt.show()


def ks_score(y_true, y_proba):
    from sklearn.metrics import roc_curve
    """
    Tính và hiển thị KS score
    y_true  : Ground truth labels (0 or 1)
    y_proba : Xác suất mô hình dự đoán cho class 1
    KS Score	Mức phân tách	Diễn giải
    0.00–0.20	Rất yếu 😢	Mô hình gần như không phân biệt được class 0 và 1
    0.20–0.40	Trung bình 😐	Mô hình có chút khả năng phân biệt
    0.40–0.70	Tốt 💪	Mô hình phân tách tốt, áp dụng thực tế được
    > 0.70	Rất tốt 🚀	Hiếm khi gặp, cần kiểm tra overfitting
    """
    fpr, tpr, thresholds = roc_curve(y_true, y_proba)
    ks = max(tpr - fpr)
    ks_threshold = thresholds[np.argmax(tpr - fpr)]

    return ks, ks_threshold


def report_lift_table(df_test, y_test, y_pred_proba, profit_col='profit_3M'):
    df_lift = df_test.copy()
    # Thêm label và score
    df_lift['y_true'] = y_test
    df_lift['y_prob'] = y_pred_proba

    df_lift = df_lift.sort_values('y_prob', ascending=False).reset_index(drop=True)

    total_positives = df_lift['y_true'].sum()
    baseline_rate = total_positives / df_lift.shape[0]

    # Thêm profit và ticker nếu có, nếu không thì tạo cột dummy
    if profit_col not in df_lift.columns:
        df_lift[profit_col] = 0
    if 'ticker' not in df_lift.columns:
        df_lift['ticker'] = 'UNK'

    # Binning
    df_lift['bin'] = pd.qcut(df_lift['y_prob'], q=5, labels=['0-20', '20-40', '40-60', '60-80', '80-100'])

    # Group và tính toán
    lift_table = df_lift.groupby('bin').agg(
        base=('y_true', 'count'),
        target=('y_true', 'sum'),
        pct_target=('y_true', 'mean'),
        average_Profit=(profit_col, 'mean'),
        ticker_nums=('ticker', pd.Series.nunique)
    ).reset_index()

    lift_table['%target'] = (lift_table['pct_target'] * 100).round(2)
    # lift_table = lift_table.drop('pct_target', axis=1)

    lift_table = lift_table.rename(columns={'bin': 'Bin score', 'average_Profit': 'average Profit'})

    lift_table['lift'] = lift_table['pct_target'] / baseline_rate

    print(lift_table[['Bin score', 'base', 'target', '%target', 'average Profit', 'ticker_nums', 'lift']].to_markdown(
        index=False))


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def split_tvt(df, cname_tvt='tvt', time_col='time', test_size=10,
              train_cutoff='2023-01-01', val_cutoff='2023-06-01'):
    """
    Split into train/val/test sets.
    - 10% of tickers are assigned to test (any time within test_cutoff range)
    - train: time < train_cutoff
    - val: time in (train_cutoff + 100 days) to val_cutoff
    - test: test tickers in range (train_cutoff, test_cutoff]
    """
    df[time_col] = pd.to_datetime(df[time_col])
    train_cutoff = pd.to_datetime(train_cutoff)
    val_cutoff = pd.to_datetime(val_cutoff)

    list_ticker = list(df['ticker'].unique())
    test_tickers = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]

    is_test_ticker = df['ticker'].isin(test_tickers)
    is_train_val_ticker = ~is_test_ticker

    is_train_time = df[time_col] < train_cutoff
    is_val_time = (df[time_col] > val_cutoff)

    # Assign tvt
    df[cname_tvt] = 'other'
    df.loc[is_train_val_ticker & is_train_time, cname_tvt] = 'train'
    df.loc[is_train_val_ticker & is_val_time, cname_tvt] = 'val'
    df.loc[is_test_ticker & is_train_time, cname_tvt] = 'test_1'
    df.loc[is_test_ticker & is_val_time, cname_tvt] = 'test_2'

    print(df[cname_tvt].value_counts())
    return df


def label_strength_4(row, label, thres_2=10, thres_1=0, thres_0=-10):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:3
        - 0: Fail
        - 1: Weak
        - 2: Strong
        - 3: Very Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= thres_2:
        return 3
    elif v >= thres_1:
        return 2
    elif v >= thres_0:
        return 1
    else:
        return 0


def label_strength_3(row, label, strong_th=10, weak_th=0):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:2
        - 0: Fail
        - 1: Weak
        - 2: Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 2
    elif v >= weak_th:
        return 1
    else:
        return 0


def label_binary(row, label, strong_th=5):
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v1(row, label, strong_th=5):
    center_dict = {
        '2W': 5,
        '1M': 10,
        '3M': 20
    }
    v = row[f'profit_{label}_center_{center_dict[label]}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v2(row, label, strong_th=5):
    center_dict = {
        '2W': 3,
        '1M': 7,
        '3M': 15
    }
    v = row[f'profit_{label}_center_{center_dict[label]}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


chunks = []
for chunk in pd.read_csv("deeplearning/dl_train.csv", chunksize=50000):
    chunks.append(chunk)
df_data_all = pd.concat(chunks, ignore_index=True)
df_data_all = df_data_all.dropna(axis=0, how='any')

# df_data_all = pd.read_parquet(path_parquet)
profit_cols = [col for col in df_data_all.columns if col.startswith('profit_')]
cname_tvt = 'tvt'
labels_tag = [cname_tvt, 'time', 'ticker', 'week', 'month'] + profit_cols
# Load data and labels
# list_eval = get_list_eval(df_data)

thres_hold = 10

for label in ['2W', '1M', '3M']:
    df_data_all[f'label_binary_{label}'] = df_data_all.apply(lambda row: label_binary(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}')

    df_data_all[f'label_binary_{label}_center_v1'] =  df_data_all.apply(lambda row: label_binary_v1(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v1')

    df_data_all[f'label_binary_{label}_center_v2'] =  df_data_all.apply(lambda row: label_binary_v1(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v2')

    # # multi 3
    # df_data_all[f'label_{label}_8'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=8), axis=1)
    # labels_tag.append(f'label_{label}_8')
    # df_data_all[f'label_{label}_12'] = df_data_all.apply(lambda row: label_strength_3(row, label,strong_th=12), axis=1)
    # labels_tag.append(f'label_{label}_12')
    # df_data_all[f'label_{label}_26'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=26), axis=1)
    # labels_tag.append(f'label_{label}_26')
    # #multi 4
    # df_data_all[f'label_{label}_-6_1_9'] = df_data_all.apply(lambda row: label_strength_4(row, label, thres_2=9, thres_1=1, thres_0=-6), axis=1)
    # labels_tag.append(f'label_{label}_-6_1_9')
    # df_data_all[f'label_{label}_-9_2_15'] = df_data_all.apply(lambda row: label_strength_4(row, label,thres_2=15, thres_1=2, thres_0=-9), axis=1)
    # labels_tag.append(f'label_{label}_-9_2_15')
    # df_data_all[f'label_{label}_-15_7_33'] = df_data_all.apply(lambda row: label_strength_4(row, label, thres_2=33, thres_1=2, thres_0=-9), axis=1)
    # labels_tag.append(f'label_{label}_-15_7_33')

df_data_all['week'] = pd.to_datetime(df_data_all['time']).dt.strftime('%Y-%W')
df_data_all['month'] = df_data_all['time'].map(lambda x: x[:7])
drops = ['Inflation_7']
# drops_1 = ['MA20_T1', 'MA200_T1', 'MA10', 'D_RSI_Max1W_MACD', 'D_CMB', 'Close_T1W', 'D_MACDdiff', 'D_CMB_XFast', 'D_RSI/D_RSI_T1', 'D_RSI/D_RSI_T1W']
labels_tag = labels_tag + drops
# df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first', inplace=True)
#%%
# df_data_all
#%%
df_data_all['month'] = df_data_all['time'].map(lambda x: x[:7])
df_data_all['week'] = pd.to_datetime(df_data_all['time']).dt.strftime('%Y-%W')
# df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first', inplace=True)
#%%
print("Data shape:", df_data_all.shape)
data_is_null = df_data_all.isnull()
mean_null = data_is_null.mean()

drop_cols = []
for col in df_data_all.columns:
    if mean_null[col] > 0.15 and col not in labels_tag:
        drop_cols.append(col)

df_data_all.drop(columns=drop_cols, inplace=True)
print("Data shape:", df_data_all.shape)

#%%
# Preprocess data
N_JOBS = 10
df_data = df_data_all.dropna(axis=0, how='any').copy()

df_data = df_data.set_index(['ticker', 'time'])

num_cols = df_data.select_dtypes(include=np.number).columns
for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    df_data = df_data[(df_data[col] > -90) & (df_data[col] < 200)]

skew_threshold = 1.0
log_applied_cols = []
for col in num_cols:
    if col in labels_tag:
        continue
    skew_val = df_data[col].skew()
    if abs(skew_val) > skew_threshold and df_data[col].min() >= 0:
        df_data[col] = np.log1p(df_data[col])
        log_applied_cols.append(col)

for col in num_cols:
    low = df_data[col].quantile(0.005)
    high = df_data[col].quantile(0.995)
    df_data[col] = df_data[col].clip(lower=low, upper=high)

df_data = df_data.sample(frac=1).reset_index(drop=True)
#%%
# Preprocess data
# N_JOBS = 10
#
# df_data = df_data_all.dropna(axis=0, how='any').copy()
#
# df_data = df_data.set_index(['ticker', 'time'])

# df_data = df_data.unstack('ticker').stack('ticker')

# Loại outlier
# for col in ['profit_2W', 'profit_1M', 'profit_3M']:
#     df_data = df_data[(df_data[col] > -90) & (df_data[col] < 200)]

# # 3. Remove cổ phiếu có missing rate > 30%
# threshold = 0.3
# missing_rate = df_data.groupby('ticker').apply(lambda x: x.isna().mean().mean())
# keep_symbols = missing_rate[missing_rate < threshold].index
# df_data = df_data.loc[df_data.index.get_level_values('ticker').isin(keep_symbols)]

# # 6. Remove features tương quan cao >0.95
# num_cols = df_data.select_dtypes(include=np.number).columns
# corr_matrix = df_data[num_cols].corr().abs()
# upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
# to_drop = [column for column in upper.columns if any(upper[column] > 0.95)]
# df_data = df_data.drop(columns=to_drop)
#
# skew_threshold = 2.0
# log_applied_cols = []
# for col in num_cols and col not in labels_tag:
#     skew_val = df_data[col].skew()
#     if abs(skew_val) > skew_threshold and df_data[col].min() >= 0:
#         # df_data[col] = np.log1p(df_data[col])
#         log_applied_cols.append(col)
#
#  # 13. Loại extreme value từng feature (clip 1st-99th percentile)
# for col in num_cols:
#     low = df_data[col].quantile(0.005)
#     high = df_data[col].quantile(0.995)
#     df_data[col] = df_data[col].clip(lower=low, upper=high)

#%% md

#%%
df_data['Volume'].skew()
#%%
# Investigate the dataimport pandas as pd
import matplotlib.pyplot as plt

df = df_data_all.dropna(axis=0, how='any').copy()
df = df_data_all.drop_duplicates(subset=['month', 'ticker'], keep='first')
# 1. Vẽ phân phối profit cho từng time frame
plt.figure(figsize=(15, 5))
for idx, col in enumerate(['profit_2W', 'profit_1M', 'profit_3M']):
    plt.subplot(1, 3, idx + 1)
    plt.hist(df[col], bins=50)
    plt.title(col)
plt.tight_layout()
plt.show()

# 2. Loại outlier (tuỳ thuộc vào chart bên trên, tạm thời cut ở -50% và +50%)
for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    df = df[(df[col] > -70) & (df[col] < 200)]


# 3. Chia label absolute profit cho từng time frame
def label_profit(x):
    if x < -15:
        return 0
    elif x < 7:
        return 1
    elif x < 33:
        return 2  # lời khá
    else:
        return 3  # lời mạnh


for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    df[f'{col}_class'] = df[col].apply(label_profit)
    print(f"Phân phối label {col}_class:")
    print(df[f'{col}_class'].value_counts(), "\n")

# 4. Chia label theo percentile (quartile) cho từng time frame
for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    # Chạy thử qcut không truyền label để biết số lượng bin thực tế
    try:
        quartile, bins = pd.qcut(df[col], q=2, retbins=True, duplicates='drop')
        num_bin = len(bins) - 1
        df[f'{col}_quartile'] = pd.qcut(df[col], q=2, labels=list(range(num_bin)), duplicates='drop')
        print(f"Phân phối label {col}_quartile:")
        print(df[f'{col}_quartile'].value_counts(), "\n")
        print(bins, "\n")
        # print(quartile)

    except Exception as e:
        print(f"Lỗi chia {col}: {e}")

# # Find insight
# # df_data = df_data_all.dropna(axis=0, how='any')
# label = ['label_2W', 'label_1M', 'label_3M']
# vi_count_unit = (df_data.nunique())
# vi_df_describe = (df_data.describe())
# vi_profit_2W_othernull = (df_data_all[df_data_all['label_2W'].notnull()].isnull().sum())
# vi_profit_1M_othernull = (df_data_all[df_data_all['label_1M'].notnull()].isnull().sum())
# vi_profit_4M_othernull = (df_data_all[df_data_all['label_3M'].notnull()].isnull().sum())
#
# for la in label:
#     # print(df_data_all[f'{la}'].value_counts())
#     print(df_data[f'{la}'].value_counts())
#%%
data_train, data_test, data_val = df_data[df_data[cname_tvt] == 'train'].reset_index(drop=True), \
    df_data[df_data[cname_tvt] == 'test'].reset_index(drop=True), \
    df_data[df_data[cname_tvt] == 'val'].reset_index(drop=True)

y_train_m, y_test_m, y_val_m = data_train[labels_tag], data_test[labels_tag], data_val[labels_tag]

X_train, X_test, X_val = (
    data_train.drop(labels_tag, axis=1),
    data_test.drop(labels_tag, axis=1),
    data_val.drop(labels_tag, axis=1)
)

#%%
# Normalize

scaler = StandardScaler()
scaler.fit(X_train)

X_train_scaled = scaler.transform(X_train)
X_val_scaled = scaler.transform(X_val)
X_test_scaled = scaler.transform(X_test)

X_train_final = X_train_scaled
X_val_final = X_val_scaled
X_test_final = X_test_scaled
#%%
# pca = PCA(n_components=0.8, random_state=42)
# pca.fit(X_train_scaled)
#
# X_train_pca = pca.transform(X_train_scaled)
# X_val_pca = pca.transform(X_val_scaled)
# X_train_pca.shape, X_val_pca.shape
#%%

#%%
# model = LogisticRegression(n_jobs=N_JOBS)
# model.fit(X_train_final, y_train)
# y_pred = model.predict_proba(X_val_final)
# acc = accuracy_score(y_val, y_pred.argmax(axis=1))
# f1 = f1_score(y_val, y_pred.argmax(axis=1), average='macro')
# y_val_onehot = label_binarize(y_val, classes=[0, 1, 2, 3])
# roc = roc_auc_score(y_val_onehot, y_pred, multi_class='ovr', average='macro')
# print(f'LogisticRegression: acc={acc * 100:.2f}% - f1={f1 * 100:.2f}% - roc={roc * 100:.2f}%')
# show_confusion_matrix(y_val, y_pred, normalize='true')
#%%
# model = DecisionTreeClassifier()
# model.fit(X_train_final, y_train)
# y_pred = model.predict_proba(X_val_final)
# acc = accuracy_score(y_val, y_pred.argmax(axis=1))
# # f1 = f1_score(y_val, y_pred.argmax(axis=1))
# # roc = roc_auc_score(y_val, y_pred[:, 1])
# f1 = f1_score(y_val, y_pred.argmax(axis=1), average='macro')
# y_val_onehot = label_binarize(y_val, classes=[0, 1, 2, 3])
# roc = roc_auc_score(y_val_onehot, y_pred, multi_class='ovr', average='macro')
# print(f'DecisionTreeClassifier: acc={acc * 100:.2f}% - f1={f1 * 100:.2f}% - roc={roc * 100:.2f}%')
# show_confusion_matrix(y_val, y_pred, normalize='true')
#%%
# model = RandomForestClassifier(n_jobs=N_JOBS, class_weight='balanced')
# model.fit(X_train_final, y_train)
# y_pred = model.predict_proba(X_val_final)
# acc = accuracy_score(y_val, y_pred.argmax(axis=1))
# f1 = f1_score(y_val, y_pred.argmax(axis=1), average='macro')
# y_val_onehot = label_binarize(y_val, classes=[0, 1, 2, 3])
# roc = roc_auc_score(y_val_onehot, y_pred, multi_class='ovr', average='macro')
# print(f'RandomForestClassifier: acc={acc * 100:.2f}% - f1={f1 * 100:.2f}% - roc={roc * 100:.2f}%')
# show_confusion_matrix(y_val, y_pred, normalize='true')

#%%

#%%
# model = GradientBoostingClassifier()
# model.fit(X_train_final, y_train)
# y_pred = model.predict_proba(X_val_final)
# acc = accuracy_score(y_val, y_pred.argmax(axis=1))
# f1 = f1_score(y_val, y_pred.argmax(axis=1), average='macro')
# y_val_onehot = label_binarize(y_val, classes=[0, 1, 2, 3])
# roc = roc_auc_score(y_val_onehot, y_pred, multi_class='ovr', average='macro')
# print(f'GradientBoostingClassifier: acc={acc * 100:.2f}% - f1={f1 * 100:.2f}% - roc={roc * 100:.2f}%')
# show_confusion_matrix(y_val, y_pred, normalize='true')
#%%
# params = {
#     'tree_method': 'hist',
#     'max_bin': 256,
#     'max_depth': 9,
#     'min_child_weight': 10,
#     'subsample': 0.8,
#     'colsample_bytree': 0.8,
#     'learning_rate': 0.01,
#     'n_estimators': 1000,
#     'predictor': 'cpu_predictor',
#     'verbosity': 1,
#     'eval_metric': ['mlogloss', 'merror'],  # dùng cho đa lớp
#     'early_stopping_rounds': 100,
#     'gamma': 0.1,
#     'lambda': 1.0,
#     'alpha': 0.5,
#     'objective': 'multi:softprob',  # cần cho bài toán đa lớp
#     'num_class': 4,  # số lớp phân loại
#     "n_jobs": N_JOBS
# }
# # class_weights = [1 / np.sum(y_train == i) for i in range(4)]
# # weights = [class_weights[int(i)] for i in y_train]
# # print(class_weights)
#
# model = xgb.XGBClassifier(**params)
# model.fit(X_train_final, y_train, eval_set=[(X_val_final, y_val)], verbose=params['verbosity'])
#
# y_pred = model.predict_proba(X_val_final)
# # y_pred = model.predict_proba(X_val_final)[:, 1]
# acc = accuracy_score(y_val, y_pred.argmax(axis=1))
# f1 = f1_score(y_val, y_pred.argmax(axis=1), average='macro')
# y_val_onehot = label_binarize(y_val, classes=[0, 1, 2, 3])
# roc = roc_auc_score(y_val_onehot, y_pred, multi_class='ovr', average='macro')
# print(f'XGBoostingClassifier: acc={acc * 100:.2f}% - f1={f1 * 100:.2f}% - roc={roc * 100:.2f}%')
# show_confusion_matrix(y_val, y_pred, normalize='true')
#%%
# # --- PHÂN TÍCH PHÂN PHỐI LABEL TRƯỚC KHI XỬ LÝ IMBALANCE ---
# print("Label distribution:", np.bincount(y_train))
# sns.countplot(x=y_train)
# plt.title("Label distribution before SMOTE")
# plt.show()
#
# from sklearn.utils.class_weight import compute_sample_weight
# # sample_weight_smote = compute_sample_weight(class_weight='balanced', y=y_train_smote)
#
# # --- ÁP DỤNG SMOTE CHO MULTICLASS ---
# from imblearn.over_sampling import SMOTE
#
# smote = SMOTE(random_state=42)
# X_train_smote, y_train_smote = smote.fit_resample(X_train_final, y_train)
#
# print("Label distribution after SMOTE:", np.bincount(y_train_smote))
# sns.countplot(x=y_train_smote)
# plt.title("Label distribution after SMOTE")
# plt.show()
#
# # --- TÍNH sample_weight SAU SMOTE (optional, thường không cần nếu đã SMOTE) ---
# # Nếu muốn thử kết hợp cả sample_weight và SMOTE:
# from sklearn.utils.class_weight import compute_sample_weight
# # sample_weight_smote = compute_sample_weight(class_weight='balanced', y=y_train_smote)

#%%
for lb in ['label_2W_8', 'label_1M_12', 'label_3M_26']:
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_test = y_test_m[lb]

    print(f"Train {lb} distribution:", np.bincount(y_train))
    sns.countplot(x=y_train)
    plt.title("Label distribution before SMOTE")
    plt.show()

    # ======= 2. (Optional) sample_weight cho train set (nên dùng nếu imbalance) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)
    scale_pos_weight = sample_weight.max() / sample_weight.min()

    # ======= 3. Định nghĩa param grid cho RandomizedSearchCV =======
    param_dist = {
        'learning_rate': [0.01, 0.03, 0.05, 0.1],
        'max_depth': [3, 5, 7, 9],
        'min_child_weight': [1, 2, 4],
        'subsample': [0.7, 0.8, 0.9, 1.0],
        'colsample_bytree': [0.7, 0.8, 0.9, 1.0],
        'gamma': [0, 0.05, 0.1],
        'reg_lambda': [1, 2, 5],
        'reg_alpha': [0, 0.5, 1.0],
        'n_estimators': [300, 500, 800, 1000],
        'max_bin': [128, 256, 512]
    }

    xgb_base = xgb.XGBClassifier(
        tree_method='hist',
        objective='binary:logistic',
        n_jobs=-1,
        eval_metric=['logloss', 'auc', 'error'],
        random_state=42
    )

    # ======= 4. RandomizedSearchCV =======
    search = RandomizedSearchCV(
        estimator=xgb_base,
        param_distributions=param_dist,
        n_iter=20,  # tăng lên nếu muốn thử nhiều tổ hợp hơn
        scoring='f1',  # hoặc 'roc_auc' nếu muốn tối ưu AUC
        cv=3,
        verbose=2,
        random_state=42,
        n_jobs=-1,
        return_train_score=True
    )
    search.fit(X_train_final, y_train, sample_weight=sample_weight)

    print("\nBest params:", search.best_params_)
    print("Best F1 (CV):", search.best_score_)

    # ======= 5. Train lại model với best params + early stopping =======
    best_params = search.best_params_
    best_params.update({
        'tree_method': 'hist',
        'objective': 'binary:logistic',
        'n_jobs': -1,
        'eval_metric': ['logloss', 'auc', 'error'],
        'random_state': 42
    })

    final_model = xgb.XGBClassifier(**best_params)
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        early_stopping_rounds=30,
        verbose=True
    )

    # ======= 6. Đánh giá trên test set =======
    y_pred_proba = final_model.predict_proba(X_test_final)[:, 1]
    y_pred = (y_pred_proba >= 0.5).astype(int)

    acc = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)
    roc = roc_auc_score(y_test, y_pred_proba)
    print(f"ACC={acc * 100:.2f}% | F1={f1 * 100:.2f}% | ROC={roc * 100:.2f}%")
    print(classification_report(y_test, y_pred, digits=4))

    # ======= 7. Confusion Matrix =======
    from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
    import matplotlib.pyplot as plt

    cm = confusion_matrix(y_test, y_pred, normalize='true')
    print("Confusion Matrix (normalized):")
    print(cm)
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=[0, 1])
    disp.plot(cmap='Blues', values_format='.2f')
    plt.title('Confusion Matrix (normalized)')
    plt.show()


#%%
# ======= 5. Train lại model với best params + early stopping =======
best_params = {'subsample': 0.7, 'reg_lambda': 1, 'reg_alpha': 0, 'n_estimators': 1000, 'min_child_weight': 1,
               'max_depth': 9, 'max_bin': 128, 'learning_rate': 0.03, 'gamma': 0.1, 'colsample_bytree': 0.8}

best_params.update({
    'tree_method': 'hist',
    'objective': 'binary:logistic',
    'n_jobs': -1,
    'eval_metric': ['logloss', 'auc', 'error'],
    'random_state': 42,
    'early_stopping_rounds': 30

})

final_model = xgb.XGBClassifier(**best_params)
final_model.fit(
    X_train_final, y_train,
    sample_weight=sample_weight,
    eval_set=[(X_val_final, y_val)],
    verbose=True
)

# ======= 6. Đánh giá trên test set =======
y_pred_proba = final_model.predict_proba(X_test_final)[:, 1]
y_pred = (y_pred_proba >= 0.5).astype(int)

acc = accuracy_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc = roc_auc_score(y_test, y_pred_proba)
print(f"ACC={acc * 100:.2f}% | F1={f1 * 100:.2f}% | ROC={roc * 100:.2f}%")
print(classification_report(y_test, y_pred, digits=4))

# ======= 7. Confusion Matrix =======
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
import matplotlib.pyplot as plt

cm = confusion_matrix(y_test, y_pred, normalize='true')
print("Confusion Matrix (normalized):")
print(cm)
disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=[0, 1])
disp.plot(cmap='Blues', values_format='.2f')
plt.title('Confusion Matrix (normalized)')
plt.show()
#%%
lb = '_4M_dsd'
'2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
#%%
for lb in ['label_3M_-15_7_33']:
    print("++++++++++++++++++++++++++++++++++++++++++++++++++++")
    print(lb)
    print(df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_test = y_test_m[lb]

    print("Label distribution before SMOTE:", np.bincount(y_train))
    # sns.countplot(x=y_train)
    # plt.title("Label distribution before SMOTE")
    # plt.show()

    # --- ÁP DỤNG SMOTE CHO MULTICLASS ---
    # from imblearn.over_sampling import SMOTE

    # smote = SMOTE(random_state=42)
    # X_train_smote, y_train_smote = smote.fit_resample(X_train_final, y_train)

    # print("Label distribution after SMOTE:", np.bincount(y_train_smote))
    # sns.countplot(x=y_train_smote)
    # plt.title("Label distribution after SMOTE")
    # plt.show()

    # --- TÍNH sample_weight SAU SMOTE (optional, thường không cần nếu đã SMOTE) ---
    # Nếu muốn thử kết hợp cả sample_weight và SMOTE:
    from sklearn.utils.class_weight import compute_sample_weight

    X_train_smote, y_train_smote = X_train_final, y_train
    sample_weight_smote = compute_sample_weight(class_weight='balanced', y=y_train_smote)

    # --- PARAM GRID CHO MULTICLASS ---
    param_grid_mc = {
        'learning_rate': [0.04, 0.06],
        'n_estimators': [500, 700, 900],
        'max_depth': [6, 7, 8],
        'min_child_weight': [1, 2, 3],
        'subsample': [0.7, 0.8, 0.9],
        'colsample_bytree': [0.7, 0.8, 0.9],
        'gamma': [0, 0.05, 0.1],
        'reg_lambda': [1, 2, 3],
        'reg_alpha': [0.8, 1.0, 1.2],
        'max_bin': [128, 256, 512]
    }

    xgb_base_mc = xgb.XGBClassifier(
        tree_method='hist',
        objective='multi:softprob',
        num_class=4,
        n_jobs=35,
        eval_metric=['mlogloss', 'merror'],
        random_state=42
    )

    search_mc = RandomizedSearchCV(
        estimator=xgb_base_mc,
        param_distributions=param_grid_mc,
        n_iter=15,
        scoring='f1_macro',
        cv=3,
        verbose=True,
        random_state=42,
        n_jobs=35,
        return_train_score=False
    )

    # --- FIT VỚI DỮ LIỆU SAU SMOTE ---
    # search_mc.fit(X_train_smote, y_train_smote, verbose=False)
    search_mc.fit(X_train_smote, y_train_smote, verbose=False, sample_weight=sample_weight_smote)

    print("\n===== BEST PARAMS & CV SCORE (SMOTE) =====")
    print("Best params:", search_mc.best_params_)
    print("Best macro F1 (CV):", search_mc.best_score_)

    # --- TRAIN FINAL MODEL VỚI DỮ LIỆU TRAIN+VAL (KHÔNG SMOTE LẠI, CHỈ GỘP TRAIN+VAL) ---
    # Lưu ý: Không dùng early_stopping ở bước này, vì không còn val set để check
    # X_train_all = np.vstack([X_train_smote, X_val_final])
    # y_train_all = np.hstack([y_train_smote, y_val])

    best_params_mc = search_mc.best_params_
    best_params_mc.update({
        'tree_method': 'hist',
        'objective': 'multi:softprob',
        'num_class': 4,
        'n_jobs': 35,
        'eval_metric': ['mlogloss', 'merror'],
        'random_state': 42,
        'n_estimators': 2000,
        'early_stopping_rounds': 70,

    })

    final_model_mc = xgb.XGBClassifier(**best_params_mc)
    final_model_mc.fit(
        X_train_smote, y_train_smote,
        eval_set=[(X_val_final, y_val)],
        sample_weight=sample_weight_smote,
        verbose=False
    )

    # --- ĐÁNH GIÁ KẾT QUẢ ---
    y_pred_prob_mc = final_model_mc.predict_proba(X_test_final)
    y_pred_mc = y_pred_prob_mc.argmax(axis=1)

    acc_mc = accuracy_score(y_test, y_pred_mc)
    f1_mc = f1_score(y_test, y_pred_mc, average='macro')
    y_test_onehot_mc = label_binarize(y_test, classes=[0, 1, 2, 3])
    roc_mc = roc_auc_score(y_test_onehot_mc, y_pred_prob_mc, multi_class='ovr', average='macro')

    print("\n===== FINAL TEST METRICS (SMOTE) =====")
    print(f'ACC={acc_mc * 100:.2f}% - F1={f1_mc * 100:.2f}% - ROC={roc_mc * 100:.2f}%')
    print(classification_report(y_test, y_pred_mc, digits=4))

    show_confusion_matrix(y_test, y_pred_prob_mc, class_names=['0', '1', '2', '3'], normalize='true')
    print("++++++++++++++++++++++++++++++++++++++++++++++++++++")

#%%
# --- TRAIN FINAL MODEL VỚI DỮ LIỆU TRAIN+VAL (SAU SMOTE) ---
# X_train_all = np.vstack([X_train_smote, X_val_final])
# y_train_all = np.hstack([y_train_smote, y_val])
# sample_weight_all = compute_sample_weight(class_weight='balanced', y=y_train_all)

best_params_mc = search_mc.best_params_
best_params_mc.update({
    'tree_method': 'hist',
    'objective': 'multi:softprob',
    'num_class': 4,
    'n_jobs': -1,
    'eval_metric': ['mlogloss', 'merror'],
    'early_stopping_rounds': 30,
    'random_state': 42
})

final_model_mc = xgb.XGBClassifier(**best_params_mc)
# final_model_mc.fit(
#     X_train_all, y_train_all,
#     # verbose=True
# )
final_model_mc.fit(
    X_train_smote, y_train_smote,
    eval_set=[(X_val_final, y_val)],
    # sample_weight=sample_weight_smote,
    # verbose=True
)

# --- ĐÁNH GIÁ KẾT QUẢ ---
y_pred_prob_mc = final_model_mc.predict_proba(X_test_final)
y_pred_mc = y_pred_prob_mc.argmax(axis=1)

acc_mc = accuracy_score(y_test, y_pred_mc)
f1_mc = f1_score(y_test, y_pred_mc, average='macro')
y_test_onehot_mc = label_binarize(y_test, classes=[0, 1, 2, 3])
roc_mc = roc_auc_score(y_test_onehot_mc, y_pred_prob_mc, multi_class='ovr', average='macro')

print("\n===== FINAL TEST METRICS (SMOTE) =====")
print(f'ACC={acc_mc * 100:.2f}% - F1={f1_mc * 100:.2f}% - ROC={roc_mc * 100:.2f}%')
print(classification_report(y_test, y_pred_mc, digits=4))

show_confusion_matrix(y_test, y_pred_prob_mc, class_names=['0', '1', '2', '3'], normalize='true')

#%%

for lb in ['label_binary_3M']:
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_test = y_test_m[lb]

    print(f"Train {lb} distribution:", np.bincount(y_train))
    sns.countplot(x=y_train)
    plt.title("Train {lb} distribution")
    plt.show()

    # ======= 2. (Optional) sample_weight cho train set (nên dùng nếu imbalance) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)
    scale_pos_weight = y_train.value_counts()[0] / y_train.value_counts()[1]

    # ======= 3. Định nghĩa param grid cho RandomizedSearchCV =======
    param_dist = {
        'learning_rate': [0.01, 0.03, 0.05, 0.1],
        'max_depth': [3, 5, 7, 9],
        'min_child_weight': [3, 5, 7],
        'subsample': [0.7, 0.8, 0.9, 1.0],
        'colsample_bytree': [0.7, 0.8, 0.9, 1.0],
        'gamma': [0, 0.05, 0.1],
        'reg_lambda': [3, 6, 9],
        'reg_alpha': [0.5, 1, 5],
        'n_estimators': [200, 300, 500],
        'max_bin': [128, 256, 512]
    }

    xgb_base = xgb.XGBClassifier(
        tree_method='hist',
        objective='binary:logistic',
        n_jobs=30,
        # eval_metric=['logloss', 'auc', 'error'],
        eval_metric=['auc'],
        random_state=42
    )

    # ======= 4. RandomizedSearchCV =======
    search = RandomizedSearchCV(
        estimator=xgb_base,
        param_distributions=param_dist,
        n_iter=25,  # tăng lên nếu muốn thử nhiều tổ hợp hơn
        scoring='roc_auc',  # hoặc 'roc_auc' nếu muốn tối ưu AUC
        cv=4,
        verbose=2,
        random_state=42,
        n_jobs=-1,
        return_train_score=True
    )
    search.fit(X_train_final, y_train, sample_weight=sample_weight, verbose=False)

    print("\nBest params:", search.best_params_)
    print("Best roc_auc (roc_auc):", search.best_score_)

    # ======= 5. Train lại model với best params + early stopping =======
    best_params = search.best_params_
    best_params.update({
        'tree_method': 'hist',
        'objective': 'binary:logistic',
        'n_jobs': -1,
        'eval_metric': 'auc',
        'random_state': 42,
        'early_stopping_rounds': 40,
    })

    final_model = xgb.XGBClassifier(**best_params)
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        verbose=False
    )

    # ======= Test overfit ============

    y_pred_proba = final_model.predict_proba(X_train_final)[:, 1]
    y_pred = (y_pred_proba >= 0.5).astype(int)

    acc = accuracy_score(y_train, y_pred)
    f1 = f1_score(y_train, y_pred)
    roc = roc_auc_score(y_train, y_pred_proba)
    ks, ks_threshold = ks_score(y_train, y_pred_proba)
    print(f"Test OVERFIT: ACC={acc * 100:.2f}% | F1={f1 * 100:.2f}% | ROC={roc * 100:.2f}%")
    print(f"KS_SCORE={ks:.2f} | KS_THRESHOLD={ks_threshold:.2f}")
    print(classification_report(y_train, y_pred, digits=4))

    cm = confusion_matrix(y_train, y_pred, normalize='true')
    print("Confusion Matrix (normalized):")
    print(cm)

    ###
    base = len(y_train)
    target = y_train.sum()
    percent_target = 100 * target / base

    tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
    report = [{
        "Threshold": f"{lb}",
        "Base": f"{base / 1000:.0f}K",
        "Target": f"{target / 1000:.0f}K",
        "%target": f"{percent_target:.0f}%",
        "AUC": f"{roc * 100:.0f}%"
    }]
    report_df = pd.DataFrame(report)
    print(report_df.to_markdown(index=False))

    report_lift_table(data_train, y_train, y_pred_proba, profit_col=f'profit_{tag}')

    print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++")
    # ======= 6. Đánh giá trên test set =======
    y_pred_proba = final_model.predict_proba(X_test_final)[:, 1]
    y_pred = (y_pred_proba >= 0.5).astype(int)

    acc = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)
    roc = roc_auc_score(y_test, y_pred_proba)
    ks, ks_threshold = ks_score(y_test, y_pred_proba)
    print(f"ACC={acc * 100:.2f}% | F1={f1 * 100:.2f}% | ROC={roc * 100:.2f}%")
    print(f"KS_SCORE={ks:.2f} | KS_THRESHOLD={ks_threshold:.2f}")

    print(classification_report(y_test, y_pred, digits=4))

    cm = confusion_matrix(y_test, y_pred, normalize='true')
    print("Confusion Matrix (normalized):")
    print(cm)

    ###
    base = len(y_test)
    target = y_test.sum()
    percent_target = 100 * target / base

    tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
    report = [{
        "Threshold": f"{lb}",
        "Base": f"{base / 1000:.0f}K",
        "Target": f"{target / 1000:.0f}K",
        "%target": f"{percent_target:.0f}%",
        "AUC": f"{roc * 100:.0f}%"
    }]
    report_df = pd.DataFrame(report)
    print(report_df.to_markdown(index=False))
    report_lift_table(data_test, y_test, y_pred_proba, profit_col=f'profit_{tag}')
    # disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=[0, 1])
    # disp.plot(cmap='Blues', values_format='.2f')
    # plt.title('Confusion Matrix (normalized)')
    # plt.show()


