#%%
import os
import sys

current_dir = '/workspace/kaffa_v2'
os.chdir(current_dir)
sys.path.insert(0, current_dir)
#%%
# Load the necessary libraries
import random

import numpy as np

random.seed(123)
import os
import sys
from joblib import Memory

from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from core_utils.redis_cache import EvalRedis
from datetime import timedelta
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import matplotlib.pyplot as plt
%matplotlib inline
import xgboost as xgb
from sklearn.preprocessing import StandardScaler
import numpy as np
import xgboost as xgb
from sklearn.metrics import f1_score, roc_auc_score, classification_report, accuracy_score
from sklearn.model_selection import train_test_split, RandomizedSearchCV, StratifiedKFold
from sklearn.preprocessing import label_binarize
from sklearn.utils.class_weight import compute_sample_weight
from sklearn.model_selection import train_test_split
from sklearn.decomposition import PCA
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.svm import SVC
from sklearn.gaussian_process import GaussianProcessClassifier
from sklearn.gaussian_process.kernels import RBF
from sklearn.neighbors import KNeighborsClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, StackingClassifier
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
from IPython.core.display_functions import display
# from ydata_profiling import ProfileReport
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
import matplotlib.pyplot as plt
# import tensorflow as tf
# print(f'TensorFlow version: {tf.__version__}')
from sklearn.preprocessing import label_binarize
memory = Memory(location=f'{JOBLIB_CACHE_DIR}_tuning', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host=REDIS_HOST, db=1)


#%%
def show_confusion_matrix(y_true, y_pred_prob, class_names=None, normalize=None, figsize=(6, 6)):
    import numpy as np
    import matplotlib.pyplot as plt
    from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
    if len(y_pred_prob.shape) > 1:
        y_pred = np.argmax(y_pred_prob, axis=1)
    else:
        y_pred = y_pred_prob
    cm = confusion_matrix(y_true, y_pred, normalize=normalize)
    print("Confusion Matrix:")
    print(cm)
    plt.figure(figsize=figsize)
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=class_names)
    disp.plot(cmap='Blues', values_format='.2f' if normalize else 'd', ax=plt.gca(), colorbar=False)
    plt.title('Confusion Matrix' + (f' (normalized: {normalize})' if normalize else ''))
    plt.show()


def ks_score(y_true, y_proba):
    from sklearn.metrics import roc_curve
    """
    Tính và hiển thị KS score
    y_true  : Ground truth labels (0 or 1)
    y_proba : Xác suất mô hình dự đoán cho class 1
    KS Score	Mức phân tách	Diễn giải
    0.00–0.20	Rất yếu 😢	Mô hình gần như không phân biệt được class 0 và 1
    0.20–0.40	Trung bình 😐	Mô hình có chút khả năng phân biệt
    0.40–0.70	Tốt 💪	Mô hình phân tách tốt, áp dụng thực tế được
    > 0.70	Rất tốt 🚀	Hiếm khi gặp, cần kiểm tra overfitting
    """
    fpr, tpr, thresholds = roc_curve(y_true, y_proba)
    ks = max(tpr - fpr)
    ks_threshold = thresholds[np.argmax(tpr - fpr)]

    return ks, ks_threshold


def report_lift_table(df_test, y_test, y_pred_proba, profit_col='profit_3M'):
    def create_qcut_bin(df, col='y_prob', q=5, label_template=None):
        if label_template is None:
            label_template = ['0-20', '20-40', '40-60', '60-80', '80-100']
        try:
            return pd.qcut(df[col], q=q, labels=label_template, duplicates='raise')
        except ValueError:
            # Tính số bin khả dụng
            unique_vals = df[col].quantile(np.linspace(0, 1, q + 1)).unique()
            real_q = len(unique_vals) - 1
            labels = label_template[:real_q]
            return pd.qcut(df[col], q=real_q, labels=labels, duplicates='drop')

    df_lift = df_test.copy()
    # Thêm label và score
    df_lift['y_true'] = y_test
    df_lift['y_prob'] = y_pred_proba

    df_lift = df_lift.sort_values('y_prob', ascending=False).reset_index(drop=True)

    total_positives = df_lift['y_true'].sum()
    baseline_rate = total_positives / df_lift.shape[0]

    # Thêm profit và ticker nếu có, nếu không thì tạo cột dummy
    if profit_col not in df_lift.columns:
        df_lift[profit_col] = 0
    if 'ticker' not in df_lift.columns:
        df_lift['ticker'] = 'UNK'

    # Binning
    df_lift['bin'] = create_qcut_bin(df_lift)

    # Group và tính toán
    lift_table = df_lift.groupby('bin', observed=False).agg(
        base=('y_true', 'count'),
        target=('y_true', 'sum'),
        pct_target=('y_true', 'mean'),
        average_Profit=(profit_col, 'mean'),
        ticker_nums=('ticker', pd.Series.nunique)
    ).reset_index()

    lift_table['%target'] = (lift_table['pct_target'] * 100).round(2)
    # lift_table = lift_table.drop('pct_target', axis=1)

    lift_table = lift_table.rename(columns={'bin': 'Bin score', 'average_Profit': 'average Profit'})

    lift_table['lift'] = lift_table['pct_target'] / baseline_rate

    print(lift_table[['Bin score', 'base', 'target', '%target', 'average Profit', 'ticker_nums', 'lift']].to_markdown(
        index=False))


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def split_tvt(pdxy, cname_tvt='tvt', test_size=10, YMD_test='2022-01-01'):
    """
    Split data into train, val, test
    """
    list_ticker = list(pdxy['ticker'].unique())
    list_ticker_test = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]

    tTest = pdxy['ticker'].isin(list_ticker_test)
    tTrain = ~tTest

    # Sort by time
    pdxy = pdxy.sort_values(by='time')

    iA = pdxy['time'] < YMD_test
    iB = pdxy['time'] > (pd.to_datetime(YMD_test) + timedelta(days=100)).strftime('%Y-%m-%d')

    pdxy[cname_tvt] = 'other'
    pdxy.loc[iA & tTrain, cname_tvt] = 'train'
    pdxy.loc[iB & tTrain, cname_tvt] = 'val'
    pdxy.loc[iA & tTest, cname_tvt] = 'test1'
    pdxy.loc[iB & tTest, cname_tvt] = 'test2'
    return pdxy


def label_strength_4(row, label):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:3
        - 0: Fail
        - 1: Weak
        - 2: Strong
        - 3: Very Strong
    """
    thres_n_5 = -12.3
    thres_n_4 = -8.3
    thres_n_3 = -4.6
    thres_n_2 = -3
    thres_n_1 = -1
    thres_0 = 0.68
    thres_1 = 4
    thres_2 = 6.2
    thres_3 = 8
    thres_4 = 10.8
    thres_5 = 19.15

    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= thres_5:
        return 5
    elif v >= thres_4:
        return 4
    elif v >= thres_3:
        return 3
    elif v >= thres_2:
        return 2
    elif v >= thres_1:
        return 1
    elif v >= thres_0:
        return 0
    elif v >= thres_n_1:
        return -1
    elif v >= thres_n_2:
        return -2
    elif v >= thres_n_3:
        return -3
    elif v >= thres_n_4:
        return -4
    elif v >= thres_n_5:
        return -5


def profit_to_score(df: pd.DataFrame, col="profit_1M", n_bins=11, new_col="score_1M",
                    lower=0.01, upper=0.99, clip=None):
    """
    Convert profit column to discrete score [-k..+k] using quantile binning,
    với option xử lý outlier.

    Args:
        df (pd.DataFrame): DataFrame có cột profit
        col (str): tên cột profit
        n_bins (int): số bin, mặc định 11 -> [-5..5]
        new_col (str): tên cột output
        lower (float): quantile thấp để winsorize (vd: 0.01 = 1%)
        upper (float): quantile cao để winsorize
        clip (tuple): (min_val, max_val), nếu muốn clip cứng
    """
    series = df[col].copy()

    # xử lý outlier
    if clip:
        series = series.clip(lower=clip[0], upper=clip[1])
    else:
        low_val = series.quantile(lower)
        high_val = series.quantile(upper)
        series = series.clip(lower=low_val, upper=high_val)

    k = n_bins // 2
    labels = list(range(-k, k + 1))

    quantiles = np.linspace(0, 1, n_bins + 1)
    bins = series.quantile(quantiles).values

    df[new_col] = pd.cut(series, bins=np.unique(bins), labels=labels, include_lowest=True).astype(int)
    return df


chunks = []
for chunk in pd.read_csv("deeplearning/dl_train.csv", chunksize=50000):
    chunks.append(chunk)
df_data_all = pd.concat(chunks, ignore_index=True)

# df_data_all = pd.read_parquet(path_parquet)
profit_cols = [col for col in df_data_all.columns if col.startswith('profit_')]
cname_tvt = 'tvt'
labels_tag = [cname_tvt, 'time', 'ticker', 'week', 'month'] + profit_cols
# Load data and labels
# list_eval = get_list_eval(df_data)

thres_hold = 20
df_data_all['score_1M'] = df_data_all.apply(lambda row: label_strength_4(row, '1M'), axis=1)
labels_tag.append('score_1M')

df_data_all['score_1M_center'] = df_data_all.apply(lambda row: label_strength_4(row, '1M_center_7'), axis=1)
labels_tag.append('score_1M_center')

df_data_all = profit_to_score(df_data_all, col='profit_1M', n_bins=11, new_col='score_1M_v2', lower=0.05, upper=0.95,
                              clip=None)
labels_tag.append('score_1M_v2')

df_data_all = profit_to_score(df_data_all, col='profit_1M_center_7', n_bins=11, new_col='score_1M_center_v2',
                              lower=0.05, upper=0.95,
                              clip=None)
labels_tag.append('score_1M_center_v2')

df_data_all['week'] = pd.to_datetime(df_data_all['time']).dt.strftime('%Y-%W')
df_data_all['month'] = df_data_all['time'].map(lambda x: x[:7])
drops = ['Inflation_7']
# drops_1 = ['MA20_T1', 'MA200_T1', 'MA10', 'D_RSI_Max1W_MACD', 'D_CMB', 'Close_T1W', 'D_MACDdiff', 'D_CMB_XFast', 'D_RSI/D_RSI_T1', 'D_RSI/D_RSI_T1W']
labels_tag = labels_tag + drops
# df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first', inplace=True)
#%%
# df_data_all = df_data_all.dropna(axis=0, how='any')
# df_deal = split_tvt(df_deal, test_size=10, YMD_test='2023-01-01')
#%%
df_data_all.columns
#%%
print("Data shape:", df_data_all.shape)
data_is_null = df_data_all.isnull()
mean_null = data_is_null.mean()

drop_cols = []
for col in df_data_all.columns:
    if mean_null[col] > 0.35 and col not in labels_tag:
        drop_cols.append(col)

print("Data shape:", df_data_all.shape)

#%%
mean_null.sort_values(ascending=False).head(20)
#%%

def summarize_nan_by_ticker(df, name_cols, ignore_cols):
    cols = [i for i in name_cols if (i in df.columns) and (i not in ignore_cols)]
    # Tổng số dòng của mỗi ticker
    total_rows = df.groupby('ticker').size().rename('total_rows')

    # Dòng có ít nhất 1 NaN trong indicator
    has_nan = df[cols].isna().any(axis=1)
    rows_with_nan = df[has_nan].groupby('ticker').size().rename('rows_with_nan')

    # Đếm số NaN theo từng cột indicator cho mỗi ticker
    nan_per_col = df.groupby('ticker')[cols].apply(lambda x: x.isna().sum())
    nan_per_col.columns = [f'nan_in_{col}' for col in nan_per_col.columns]

    # Gộp tất cả vào 1 DataFrame
    result = pd.concat([total_rows, rows_with_nan, nan_per_col], axis=1).fillna(0).astype(int)

    return result
#%%
import pandas as pd

pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)  # Không wrap dòng
pd.set_option('display.max_colwidth', None)
#%%
nan_report = summarize_nan_by_ticker(df_data_all.drop(drop_cols, axis=1), mean_null.sort_values().to_frame().index,
                                     labels_tag)
#%%

#%%
# df_month = df_data_all.drop_duplicates(subset=['month', 'ticker'], keep='first')
# df_month = df_month.sort_values(['ticker', 'month'], ascending=[True, True]).reset_index(drop=True)
# df_month.to_csv('deeplearning/dl_train_month.csv', index=False)

# df_week = df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first')
# df_week = df_week.sort_values(['ticker', 'week'], ascending=[True, True]).reset_index(drop=True)
# df_week.to_csv('deeplearning/dl_train_week.csv', index=False)

#%%
# Preprocess data
N_JOBS = 35
df_data = df_data_all.drop_duplicates(subset=['ticker', 'month'], keep='first')

num_cols = df_data.select_dtypes(include=np.number).columns
for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    df_data = df_data[(df_data[col] > -90) & (df_data[col] < 200)]

skew_threshold = 1.0
log_applied_cols = []
for col in num_cols:
    if col in labels_tag:
        continue
    skew_val = df_data[col].skew()
    if abs(skew_val) > skew_threshold and df_data[col].min() >= 0:
        df_data[col] = np.log1p(df_data[col])
        log_applied_cols.append(col)

for col in num_cols:
    low = df_data[col].quantile(0.005)
    high = df_data[col].quantile(0.995)
    df_data[col] = df_data[col].clip(lower=low, upper=high)

# df_data = df_data.sample(frac=1).reset_index(drop=True)
df_data = df_data.sort_values(['ticker', 'month'], ascending=[True, True]).reset_index(drop=True)
# df_data.to_csv(output_path, index=False)
#%%
# Preprocess data
# N_JOBS = 10
#
# df_data = df_data_all.dropna(axis=0, how='any').copy()
#
# df_data = df_data.set_index(['ticker', 'time'])

# df_data = df_data.unstack('ticker').stack('ticker')

# Loại outlier
# for col in ['profit_2W', 'profit_1M', 'profit_3M']:
#     df_data = df_data[(df_data[col] > -90) & (df_data[col] < 200)]

# # 3. Remove cổ phiếu có missing rate > 30%
# threshold = 0.3
# missing_rate = df_data.groupby('ticker').apply(lambda x: x.isna().mean().mean())
# keep_symbols = missing_rate[missing_rate < threshold].index
# df_data = df_data.loc[df_data.index.get_level_values('ticker').isin(keep_symbols)]

# # 6. Remove features tương quan cao >0.95
# num_cols = df_data.select_dtypes(include=np.number).columns
# corr_matrix = df_data[num_cols].corr().abs()
# upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
# to_drop = [column for column in upper.columns if any(upper[column] > 0.95)]
# df_data = df_data.drop(columns=to_drop)
#
# skew_threshold = 2.0
# log_applied_cols = []
# for col in num_cols and col not in labels_tag:
#     skew_val = df_data[col].skew()
#     if abs(skew_val) > skew_threshold and df_data[col].min() >= 0:
#         # df_data[col] = np.log1p(df_data[col])
#         log_applied_cols.append(col)
#
#  # 13. Loại extreme value từng feature (clip 1st-99th percentile)
# for col in num_cols:
#     low = df_data[col].quantile(0.005)
#     high = df_data[col].quantile(0.995)
#     df_data[col] = df_data[col].clip(lower=low, upper=high)

#%% md

#%%
# Investigate the dataimport pandas as pd
import matplotlib.pyplot as plt

df = df_data_all.drop_duplicates(subset=['month', 'ticker'], keep='first')

# 1. Vẽ phân phối profit cho từng time frame
plt.figure(figsize=(15, 5))
for idx, col in enumerate(['profit_2W', 'profit_1M', 'profit_3M']):
    plt.subplot(1, 3, idx + 1)
    plt.hist(df[col], bins=50)
    plt.title(col)
plt.tight_layout()
plt.show()

# 2. Loại outlier (tuỳ thuộc vào chart bên trên, tạm thời cut ở -50% và +50%)
for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    df = df[(df[col] > -100) & (df[col] < 200)]


# 3. Chia label absolute profit cho từng time frame
def label_profit(x):
    if x < 10:
        return 0
    # elif x < 7:
    # return 1
    # elif x < 33:
    # return 2   # lời khá
    else:
        return 1  # lời mạnh


for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    df[f'{col}_class'] = df[col].apply(label_profit)
    print(f"Phân phối label {col}_class:")
    print(df[f'{col}_class'].value_counts(), "\n")

# 4. Chia label theo percentile (quartile) cho từng time frame
for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    # Chạy thử qcut không truyền label để biết số lượng bin thực tế
    try:
        quartile, bins = pd.qcut(df[col], q=4, retbins=True, duplicates='drop')
        num_bin = len(bins) - 1
        df[f'{col}_quartile'] = pd.qcut(df[col], q=4, labels=list(range(num_bin)), duplicates='drop')
        print(f"Phân phối label {col}_quartile:")
        print(df[f'{col}_quartile'].value_counts(), "\n")
        print(bins, "\n")
        # print(quartile)

    except Exception as e:
        print(f"Lỗi chia {col}: {e}")

# # Find insight
# # df_data = df_data_all.dropna(axis=0, how='any')
# label = ['label_2W', 'label_1M', 'label_3M']
# vi_count_unit = (df_data.nunique())
# vi_df_describe = (df_data.describe())
# vi_profit_2W_othernull = (df_data_all[df_data_all['label_2W'].notnull()].isnull().sum())
# vi_profit_1M_othernull = (df_data_all[df_data_all['label_1M'].notnull()].isnull().sum())
# vi_profit_4M_othernull = (df_data_all[df_data_all['label_3M'].notnull()].isnull().sum())
#
# for la in label:
#     # print(df_data_all[f'{la}'].value_counts())
#     print(df_data[f'{la}'].value_counts())
#%%
data_train, data_test1, data_test2, data_val = (
    df_data[df_data[cname_tvt] == 'train'].reset_index(drop=True), \
    df_data[df_data[cname_tvt] == 'test1'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'test2'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'val'].reset_index(drop=True)
)
y_train_m, y_test1_m, y_test2_m, y_val_m = data_train[labels_tag], data_test1[labels_tag], data_test2[labels_tag], \
    data_val[labels_tag]

X_train, X_test1, X_test2, X_val = (
    data_train.drop(labels_tag, axis=1),
    data_test1.drop(labels_tag, axis=1),
    data_test2.drop(labels_tag, axis=1),
    data_val.drop(labels_tag, axis=1)
)

#%%
# Normalize

scaler = StandardScaler()
scaler.fit(X_train)

X_train_scaled = scaler.transform(X_train)
X_val_scaled = scaler.transform(X_val)
X_test1_scaled = scaler.transform(X_test1)
X_test2_scaled = scaler.transform(X_test2)

X_train_final = X_train_scaled
X_val_final = X_val_scaled
X_test1_final = X_test1_scaled
X_test2_final = X_test2_scaled
#%%
X_train
#%%

#%%
# # --- PHÂN TÍCH PHÂN PHỐI LABEL TRƯỚC KHI XỬ LÝ IMBALANCE ---
# print("Label distribution:", np.bincount(y_train))
# sns.countplot(x=y_train)
# plt.title("Label distribution before SMOTE")
# plt.show()

# from sklearn.utils.class_weight import compute_sample_weight
# # sample_weight_smote = compute_sample_weight(class_weight='balanced', y=y_train_smote)

# # --- ÁP DỤNG SMOTE CHO MULTICLASS ---
# from imblearn.over_sampling import SMOTE

# smote = SMOTE(random_state=42)
# X_train_smote, y_train_smote = smote.fit_resample(X_train_final, y_train)

# print("Label distribution after SMOTE:", np.bincount(y_train_smote))
# sns.countplot(x=y_train_smote)
# plt.title("Label distribution after SMOTE")
# plt.show()

# # --- TÍNH sample_weight SAU SMOTE (optional, thường không cần nếu đã SMOTE) ---
# # Nếu muốn thử kết hợp cả sample_weight và SMOTE:
# from sklearn.utils.class_weight import compute_sample_weight
# # sample_weight_smote = compute_sample_weight(class_weight='balanced', y=y_train_smote)

#%%

for lb in ['score_1M', 'score_1M_center', 'score_1M_v2', 'score_1M_center_v2']:
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    sns.countplot(x=y_train)
    plt.title("Train {lb} distribution")
    plt.show()

    # ======= 2. (Optional) sample_weight cho train set (nên dùng nếu imbalance) =======
    # sample_weight = compute_sample_weight(class_weight='balanced', y=np.sign(y_train))
    # scale_pos_weight = y_train.value_counts()[0] / y_train.value_counts()[1]

    # ======= 3. Định nghĩa param grid cho RandomizedSearchCV =======
    param_dist = {
        'learning_rate': [0.01, 0.05, 0.1, 0.15],
        'max_depth': [3, 4, 6],  #độ sâu của cây
        'min_child_weight': [1, 5, 7],  # tối thiểu số lượng sample trong 1 node trước khi split
        'subsample': [0.7, 0.8, 1.0],  # lấy mẫu hàng mỗi cây
        'colsample_bytree': [0.7, 0.8, 1.0],  # lấy mẫu cột mỗi cây
        'gamma': [0, 0.05, 0.1],  # regularization
        'reg_lambda': [3, 6, 9],  # regularization
        'reg_alpha': [0.5, 1.0, 5.0],  # regularization
        'n_estimators': [300, 700, 1000, 1500],  #số cây tối đa; early stopping sẽ cắt sớm.
        'max_bin': [128, 256, 512],
        # 'early_stopping_rounds': [50, 100, 200]
    }


    xgb_base = xgb.XGBRegressor(
        # tree_method="gpu_hist" if args.gpu else "hist",
        # predictor="gpu_predictor" if args.gpu else "auto",
        tree_method='hist',
        objective="reg:squarederror",  # regression
        n_estimators=200,
        learning_rate=0.1,
        max_depth=6,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42
    )

    # ======= 4. RandomizedSearchCV =======
    search = RandomizedSearchCV(
        estimator=xgb_base,
        param_distributions=param_dist,
        n_iter=25,  # tăng lên nếu muốn thử nhiều tổ hợp hơn
        scoring='r2',  # hoặc 'roc_auc' nếu muốn tối ưu AUC
        cv=4,
        verbose=2,
        random_state=42,
        n_jobs=20,
        return_train_score=False
    )
    search.fit(X_train_final, y_train, eval_set=[(X_val_final, y_val)], verbose=False)

    print("\nBest params:", search.best_params_)
    print("Best r2 (r2):", search.best_score_)

    # ======= 5. Train lại model với best params + early stopping =======
    best_params = search.best_params_
    best_params.update({
        'tree_method': 'hist',
        'objective': 'reg:squarederror',
        'n_jobs': -1,
        'random_state': 42,
        'early_stopping_rounds': 40,
    })

    final_model = xgb.XGBRegressor(**best_params)
    final_model.fit(
        X_train_final, y_train,
        eval_set=[(X_val_final, y_val)],
        verbose=False
    )

    # ======= Test overfit ============

    y_pred = final_model.predict(X_train_final)

    mse = mean_squared_error(y_train, y_pred)
    mae = mean_absolute_error(y_train, y_pred)
    r2 = r2_score(y_train, y_pred)
    print(f"Train OVERFIT: MSE={mse:.4f} | MAE={mae:.4f} | R2={r2:.4f}")

    # Report summary
    base = len(y_train)
    report = [{
        "Label": f"{lb}",
        "Base": f"{base / 1000:.0f}K",
        "MSE": f"{mse:.4f}",
        "MAE": f"{mae:.4f}",
        "R2": f"{r2:.4f}"
    }]
    report_df = pd.DataFrame(report)
    print(report_df.to_markdown(index=False))

    print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++")
    # ======= 6. Đánh giá trên test set =======
    for X_test_final, y_test, data_test in [(X_test1_final, y_test1, data_test1), (X_test2_final, y_test2, data_test2)]:
        y_pred = final_model.predict(X_test_final)

        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        print(f"Test: MSE={mse:.4f} | MAE={mae:.4f} | R2={r2:.4f}")

        base = len(y_test)
        report = [{
            "Label": f"{lb}",
            "Base": f"{base / 1000:.0f}K",
            "MSE": f"{mse:.4f}",
            "MAE": f"{mae:.4f}",
            "R2": f"{r2:.4f}"
        }]
        report_df = pd.DataFrame(report)
        print(report_df.to_markdown(index=False))

        print("---------------------------------------------------------------------------")



#%%
################################################
## Classify
label_list_mc = ['score_1M', 'score_1M_center', 'score_1M_v2', 'score_1M_center_v2']

for lb in label_list_mc:
    print('Phân bố tổng thể', pd.Series(df_data[lb]).value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    # hiển thị phân bố train
    print(f"Train {lb} distribution:", np.bincount(np.asarray(y_train, dtype=int)))
    sns.countplot(x=np.asarray(y_train, dtype=int))
    plt.title(f"Train {lb} distribution")
    plt.show()

    # ======= sample_weight (đa lớp vẫn ok) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)

    # ======= param grid =======
    param_dist = {
        'learning_rate':   [0.01, 0.03, 0.05, 0.1],
        'max_depth':       [3, 5, 7, 9],
        'min_child_weight':[3, 5, 7],
        'subsample':       [0.7, 0.8, 0.9, 1.0],
        'colsample_bytree':[0.7, 0.8, 0.9, 1.0],
        'gamma':           [0, 0.05, 0.1],
        'reg_lambda':      [3, 6, 9],
        'reg_alpha':       [0.5, 1, 5],
        'n_estimators':    [300, 500, 800],
        'max_bin':         [128, 256, 512]
    }

    num_class = int(pd.Series(y_train).nunique())

    xgb_base = xgb.XGBClassifier(
        tree_method='hist',
        objective='multi:softprob',
        num_class=num_class,
        n_jobs=-1,
        eval_metric='mlogloss',
        random_state=42
    )

    # ======= RandomizedSearchCV =======
    cv = StratifiedKFold(n_splits=4, shuffle=True, random_state=42)
    search = RandomizedSearchCV(
        estimator=xgb_base,
        param_distributions=param_dist,
        n_iter=30,
        scoring='neg_log_loss',            # hoặc 'roc_auc_ovr' nếu muốn tối ưu AUC đa lớp
        cv=cv,
        verbose=2,
        random_state=42,
        n_jobs=-1,
        return_train_score=False
    )
    search.fit(X_train_final, y_train, sample_weight=sample_weight, verbose=False)

    print("\nBest params:", search.best_params_)
    print("Best CV (neg_log_loss):", search.best_score_)

    # ======= Train lại với best params + early stopping =======
    best_params = dict(search.best_params_)
    final_model = xgb.XGBClassifier(
        **best_params,
        tree_method='hist',
        objective='multi:softprob',
        num_class=num_class,
        n_jobs=-1,
        eval_metric='mlogloss',
        random_state=42
    )
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        early_stopping_rounds=40,
        verbose=False
    )

    # ======= Đánh giá TRAIN (check overfit nhẹ) =======
    proba_tr = final_model.predict_proba(X_train_final)
    y_pred_tr = np.argmax(proba_tr, axis=1)

    acc_tr = accuracy_score(y_train, y_pred_tr)
    f1m_tr = f1_score(y_train, y_pred_tr, average='macro')
    try:
        auc_tr = roc_auc_score(y_train, proba_tr, multi_class='ovr', average='macro')
    except ValueError:
        auc_tr = np.nan

    print(f"[TRAIN] ACC={acc_tr*100:.2f}% | F1_macro={f1m_tr*100:.2f}% | AUC_ovr_macro={auc_tr if np.isnan(auc_tr) else auc_tr*100:.2f}")
    print(classification_report(y_train, y_pred_tr, digits=4))
    cm_tr = confusion_matrix(y_train, y_pred_tr, normalize='true')
    print("Confusion Matrix (normalized) - TRAIN:\n", cm_tr)

    # ======= Đánh giá TEST =======
    for X_test_final, y_test, data_test in [(X_test1_final, y_test1, data_test1), (X_test2_final, y_test2, data_test2)]:

        proba_te = final_model.predict_proba(X_test_final)
        y_pred_te = np.argmax(proba_te, axis=1)

        acc = accuracy_score(y_test, y_pred_te)
        f1m = f1_score(y_test, y_pred_te, average='macro')
        try:
            auc = roc_auc_score(y_test, proba_te, multi_class='ovr', average='macro')
        except ValueError:
            auc = np.nan

        print(f"[TEST] ACC={acc*100:.2f}% | F1_macro={f1m*100:.2f}% | AUC_ovr_macro={auc if np.isnan(auc) else auc*100:.2f}")
        print(classification_report(y_test, y_pred_te, digits=4))
        cm = confusion_matrix(y_test, y_pred_te, normalize='true')
        print("Confusion Matrix (normalized) - TEST:\n", cm)

        # ======= Báo cáo ngắn gọn =======
        base = len(y_test)
        counts = pd.Series(y_test).value_counts().sort_index()
        percent_each = (counts / base * 100).round(1).astype(str) + '%'
        report = [{
            "Label": lb,
            "Classes": num_class,
            "Base": base,
            "ACC": f"{acc*100:.1f}%",
            "F1_macro": f"{f1m*100:.1f}%",
            "AUC_ovr_macro": ("NA" if np.isnan(auc) else f"{auc*100:.1f}%"),
            "Distrib_test": percent_each.to_dict()
        }]
        print(pd.DataFrame(report).to_markdown(index=False))

        print("======================================================")
#%%
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Giả định bạn đã có X_train và search.best_estimator_
xgb_model = search.best_estimator_

# Lấy tên cột gốc
feature_names = X_train.columns.tolist()
importances = xgb_model.feature_importances_

# Mapping: F{i} → tên cột
feat_imp = pd.Series(importances, index=feature_names)
top_indices = np.argsort(importances)[-40:-20]  # Top 20

# Vẽ biểu đồ
plt.figure(figsize=(15, 6))
plt.barh(range(len(top_indices)), importances[top_indices], color='skyblue')
plt.yticks(range(len(top_indices)), [feature_names[i] for i in top_indices])
plt.xlabel("Feature Importance")
plt.title("Top 20 Feature Importances (XGBoost)")
plt.tight_layout()
plt.show()

# In mapping F→column
print("📌 Mapping F→column:")
for i in top_indices:
    print(f"F{i} → {feature_names[i]}")
#%%
# ['Inflation_7', 
#  ['MA20_T1', 'MA200_T1', 'MA10', 'D_RSI_Max1W_MACD', 'D_CMB', 'Close_T1W', 'D_MACDdiff', 'D_CMB_XFast', 'D_RSI/D_RSI_T1', 'D_RSI/D_RSI_T1W']
#%%
# import pandas as pd
# import numpy as np

# # Giả sử:
# # feature_names: list tên cột (đã lưu trước khi scale)
# # importances: array model.feature_importances_
# # mi: array mutual_info_classif

# # 1) Tạo DataFrame để dễ xử lý
# df_feat = pd.DataFrame({
#     'feature': feature_names,
#     'importance': importances,
#     'mutual_info': mi
# })

# # 2) Đặt ngưỡng
# mi_thresh  = 0.01
# imp_thresh = 0.005

# # 3) Lọc ra những feature "yếu" (cả MI và importance đều dưới ngưỡng)
# weak_feats = df_feat[
#     (df_feat['mutual_info']  < mi_thresh) &
#     (df_feat['importance']   < imp_thresh)
# ]['feature'].tolist()

# print(f"Found {len(weak_feats)} weak features, example:\n", weak_feats[:10])

# # 4) Tạo X_filtered chỉ giữ lại feature tốt
# good_feats = [f for f in feature_names if f not in weak_feats]

# # X_train_filtered = X_train_df[good_feats]
# # X_val_filtered   = X_val_df[good_feats]  # nếu có tập validation
#%%
# # ======= 5. Train lại model với best params + early stopping =======
# best_params = search.best_params_
# best_params.update({
#     'tree_method': 'hist',
#     'objective': 'binary:logistic',
#     'n_jobs': -1,
#     'eval_metric': 'auc',
#     'random_state': 42,
#     'early_stopping_rounds':40,
# })

# final_model = xgb.XGBClassifier(**best_params)
# final_model.fit(
#     X_train_final, y_train,
#     sample_weight=sample_weight,
#     eval_set=[(X_val_final, y_val)],
#     verbose=False
# )


# # ======= Test overfit ============

# y_pred_proba = final_model.predict_proba(X_train_final)[:,1]
# y_pred = (y_pred_proba >= 0.5).astype(int)

# acc = accuracy_score(y_train, y_pred)
# f1 = f1_score(y_train, y_pred)
# roc = roc_auc_score(y_train, y_pred_proba)
# ks, ks_threshold = ks_score(y_train, y_pred_proba)
# print(f"Test OVERFIT: ACC={acc*100:.2f}% | F1={f1*100:.2f}% | ROC={roc*100:.2f}%")
# print(f"KS_SCORE={ks:.2f} | KS_THRESHOLD={ks_threshold:.2f}")
# print(classification_report(y_train, y_pred, digits=4))

# cm = confusion_matrix(y_train, y_pred, normalize='true')
# print("Confusion Matrix (normalized):")
# print(cm)

# ###
# base = len(y_train)
# target = y_train.sum()
# percent_target = 100 * target / base

# tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
# report = [{
#     "Threshold": f"{lb}",
#     "Base": f"{base/1000:.0f}K",
#     "Target": f"{target/1000:.0f}K",
#     "%target": f"{percent_target:.0f}%",
#     "AUC": f"{roc*100:.0f}%"
# }]
# report_df = pd.DataFrame(report)
# print(report_df.to_markdown(index=False))

# report_lift_table(data_train, y_train, y_pred_proba, profit_col=f'profit_{tag}')

# print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++")
# # ======= 6. Đánh giá trên test set =======
# y_pred_proba = final_model.predict_proba(X_test_final)[:,1]
# y_pred = (y_pred_proba >= 0.5).astype(int)

# acc = accuracy_score(y_test, y_pred)
# f1 = f1_score(y_test, y_pred)
# roc = roc_auc_score(y_test, y_pred_proba)
# ks, ks_threshold = ks_score(y_test, y_pred_proba)
# print(f"ACC={acc*100:.2f}% | F1={f1*100:.2f}% | ROC={roc*100:.2f}%")
# print(f"KS_SCORE={ks:.2f} | KS_THRESHOLD={ks_threshold:.2f}")

# print(classification_report(y_test, y_pred, digits=4))

# cm = confusion_matrix(y_test, y_pred, normalize='true')
# print("Confusion Matrix (normalized):")
# print(cm)

# ###
# base = len(y_test)
# target = y_test.sum()
# percent_target = 100 * target / base

# tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
# report = [{
#     "Threshold": f"{lb}",
#     "Base": f"{base/1000:.0f}K",
#     "Target": f"{target/1000:.0f}K",
#     "%target": f"{percent_target:.0f}%",
#     "AUC": f"{roc*100:.0f}%"
# }]
# report_df = pd.DataFrame(report)
# print(report_df.to_markdown(index=False))
# report_lift_table(data_test, y_test, y_pred_proba, profit_col=f'profit_{tag}')
# # disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=[0, 1])
# # disp.plot(cmap='Blues', values_format='.2f')
# # plt.title('Confusion Matrix (normalized)')
# # plt.show()
